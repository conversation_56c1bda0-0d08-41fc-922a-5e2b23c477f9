# 常用模式和最佳实践

- 客户管理模块代码模式：
1. 前端使用Vue3 Composition API，遵循SmartAdmin标准页面布局规范
2. 查询表单使用smart-query-form类，表格使用TableOperator组件
3. 权限控制使用v-privilege指令和@SaCheckPermission注解
4. 数据查询支持分页、筛选、导出功能
5. 使用中文字段名的VO对象（如客户唯一编码、总销售额等）
6. 后端采用四层架构：Controller->Service->Dao->Mapper
7. 支持多数据源查询（默认数据库和lirun数据库）
- 链接视角复购功能增强：实现回购日期筛选功能，支持"付款日期范围内的客户群体在回购日期范围内的复购行为分析"。修改了CustomerLinkQueryForm.java添加回购日期字段，重构了CustomerLinkMapper.xml的SQL查询逻辑，实现基准客户群体与回购行为的交集计算。
