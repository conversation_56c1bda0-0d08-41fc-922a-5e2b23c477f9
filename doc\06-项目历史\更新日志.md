# SmartAdmin 更新日志

## 2025-08-02 15:30

### 🔄 链接视角页面回购日期筛选功能

**功能概述**：
为链接视角页面新增回购日期筛选功能，在付款日期基础上提供更精确的复购分析能力。

**核心特性**：

#### 1. 双重日期筛选机制
- **付款日期**: 定义整体分析的时间窗口
- **回购日期**: 在付款日期范围内进一步限定回购行为的时间范围
- **智能约束**: 回购日期范围自动限制在付款日期范围内，确保数据逻辑正确性

#### 2. 智能默认值同步
- **自动同步**: 选择付款日期后，回购日期自动设置为相同范围
- **实时调整**: 付款日期变更时，回购日期自动调整到新的有效范围内
- **边界处理**: 确保回购日期始终在付款日期范围内，避免无效查询

#### 3. 精确的用户交互优化

##### 禁用日期点击检测
- **事件委托机制**: 使用DOM事件委托监听日期选择器内的点击事件
- **精确点击检测**: 实现对禁用日期的精确点击检测
- **智能日期解析**: 多种方法解析被点击的日期（title属性、DOM结构解析）

##### 智能提示系统
- **差异化提示**: 根据点击的禁用日期位置显示不同的提示信息
  - 早于付款开始日期：提示不能早于付款开始日期
  - 晚于付款结束日期：提示不能晚于付款结束日期
- **即时反馈**: 点击禁用日期时立即显示具体的约束说明
- **防抖机制**: 避免重复提示消息，提升用户体验

##### 生命周期管理
- **事件监听器管理**: 组件挂载时添加，卸载时移除
- **资源清理**: 确保无内存泄漏，维护应用性能

### 📊 技术实现要点

#### 1. 前端组件架构
- **Vue 3 Composition API**: 使用响应式数据和组合式函数
- **Ant Design Vue**: 基于a-range-picker组件实现日期选择
- **事件处理**: 完善的日期变更和约束处理逻辑

#### 2. 数据绑定与状态管理
```javascript
// 查询表单状态
const queryForm = reactive({
  paymentDateBegin: null,
  paymentDateEnd: null,
  repurchaseDateBegin: null,    // 新增回购开始日期
  repurchaseDateEnd: null,      // 新增回购结束日期
});

// 日期范围响应式变量
const paymentDateRange = ref([]);
const repurchaseDateRange = ref([]);  // 新增回购日期范围
```

#### 3. 约束逻辑实现
```javascript
// 回购日期禁用逻辑
function disabledRepurchaseDate(current) {
    if (!paymentDateRange.value || paymentDateRange.value.length !== 2) {
        return true; // 未选择付款日期时，禁用所有回购日期
    }

    const paymentStart = paymentDateRange.value[0];
    const paymentEnd = paymentDateRange.value[1];

    // 回购日期必须在付款日期范围内
    return current && (
        current.isBefore(paymentStart, 'day') ||
        current.isAfter(paymentEnd, 'day')
    );
}
```

### 🎯 业务价值

#### 1. 精确复购分析
- **时间窗口控制**: 可以分析特定时间段内的回购行为
- **季节性分析**: 支持分析节假日、促销期等特定时期的回购模式
- **周期性研究**: 便于研究不同时间段的客户回购周期

#### 2. 营销策略优化
- **精准营销**: 基于特定时间段的回购数据制定营销策略
- **活动效果评估**: 评估特定营销活动对回购行为的影响
- **客户生命周期管理**: 更好地理解客户在不同时期的购买行为

#### 3. 数据分析增强
- **对比分析**: 可以对比不同时间段的回购表现
- **趋势分析**: 识别回购行为的时间趋势和模式
- **异常检测**: 发现特定时期的异常回购行为

### 🔧 用户体验设计

#### 1. 直观的约束提示
- **视觉禁用**: 超出范围的日期显示为灰色不可选状态
- **即时反馈**: 点击禁用日期时立即显示具体的约束说明
- **智能提示**: 根据点击位置显示不同的提示信息

#### 2. 便捷的操作流程
- **一键同步**: 选择付款日期后回购日期自动同步
- **智能调整**: 付款日期变更时回购日期自动适应
- **快速重置**: 支持一键清空所有日期选择

#### 3. 一致的交互体验
- **统一样式**: 与付款日期选择器保持一致的外观和行为
- **预设范围**: 支持相同的快速时间范围选择
- **响应式设计**: 适配不同屏幕尺寸和设备

### 📈 功能测试验证

#### 1. 基础功能测试
- ✅ 回购日期选择器正常显示和交互
- ✅ 付款日期变更时回购日期自动同步
- ✅ 日期范围约束逻辑正确执行
- ✅ 禁用日期正确显示为灰色不可选状态

#### 2. 交互体验测试
- ✅ 点击禁用日期显示正确的提示信息
- ✅ 早于付款开始日期的提示："回购日期不能早于付款开始日期（YYYY-MM-DD）"
- ✅ 晚于付款结束日期的提示："回购日期不能晚于付款结束日期（YYYY-MM-DD）"
- ✅ 事件监听器正确添加和移除，无内存泄漏

#### 3. 边界情况测试
- ✅ 未选择付款日期时，所有回购日期均被禁用
- ✅ 付款日期清空时，回购日期自动清空
- ✅ 组件卸载时事件监听器正确清理

### 🚀 后续优化计划

- **数据持久化**: 考虑将用户的日期选择偏好保存到本地存储
- **快捷选择**: 增加更多预设的时间范围选择选项
- **数据导出**: 支持基于回购日期筛选的数据导出功能
- **统计图表**: 在统计分析中体现回购日期筛选的影响

## 2025-01-17 16:30

### 🔧 平台货品名称查询代码移除

**功能优化**：
移除平台货品选择功能中的平台货品名称查询逻辑，简化数据库查询复杂度。

**技术实现**：

#### 1. 数据库查询简化
- **移除复杂关联**: 删除了从`lirun.订单明细`表中查询平台货品名称的LEFT JOIN
- **查询优化**: 移除了使用窗口函数`ROW_NUMBER() OVER`的复杂查询逻辑
- **性能提升**: 减少了数据库查询的复杂度和执行时间

#### 2. 代码修改内容
- **Mapper XML**: 简化`PlatformProductMapper.xml`中的查询语句
- **字段保留**: 保留`PlatformProductVO`中的`productName`字段，但设置为空字符串
- **查询维持**: 保持货品ID、店铺名称、销量等核心字段的查询功能

#### 3. 前端界面影响
- **字段保留**: 前端表格中的平台货品名称列保留，为后续功能扩展预留
- **数据显示**: 平台货品名称列暂时显示为空，不影响其他功能正常使用
- **布局不变**: 表格布局和列宽配置保持不变

### 📊 查询性能优化

**优化效果**：
- **查询简化**: 从三表关联简化为两表关联查询
- **去除窗口函数**: 移除了复杂的分组排序逻辑
- **执行效率**: 提升了分页查询的响应速度
- **维护性**: 降低了SQL语句的复杂度，便于后续维护

**当前查询结构**：
```sql
SELECT 
    p.货品ID as goodsId,
    p.店铺 as shopName,
    CONCAT(p.货品ID, ' - ', p.店铺) as displayName,
    '' as productName,  -- 暂时设为空字符串
    COALESCE(s.salesVolume, 0) as salesVolume
FROM lirun.平台货品id p
LEFT JOIN (
    SELECT 平台货品ID, SUM(数量) as salesVolume 
    FROM lirun.订单明细 
    WHERE 平台货品ID IS NOT NULL AND 平台货品ID != ''
    GROUP BY 平台货品ID
) s ON p.货品ID = s.平台货品ID
```

### 🎯 后续计划

- **功能预留**: 保留平台货品名称字段，便于后续优化实现
- **性能优先**: 当前优先保证查询性能和系统稳定性
- **逐步完善**: 后续可根据业务需求重新实现平台货品名称功能

## 2025-01-17 15:00

### 🛍️ 平台货品商家编码字段新增

**功能概述**：
在平台货品选择弹窗中新增商家编码字段，显示每个货品ID对应的主要商家编码信息。

**技术实现**：

#### 1. 数据源分析
- **数据来源**: `lirun.订单明细`表中的`商家编码`字段
- **数据特点**: 一个货品ID可能对应多个商家编码
- **选择策略**: 选择出现频次最高的商家编码作为主要商家编码

#### 2. 后端数据处理
- **复杂关联查询**: 使用窗口函数`ROW_NUMBER() OVER (PARTITION BY 平台货品ID ORDER BY COUNT(*) DESC)`
- **频次统计**: 按货品ID和商家编码分组统计出现次数
- **主编码选取**: 每个货品ID选择频次最高的商家编码

#### 3. 前端界面布局
- **列顺序调整**: 货品ID → **商家编码** → 销量 → 店铺名称
- **列宽优化**: 
  - 货品ID: 150px
  - **商家编码: 180px** (新增)
  - 销量: 80px  
  - 店铺名称: 150px
- **样式设计**: 商家编码使用蓝色字体突出显示，无数据时显示"-"

#### 4. 数据示例
基于实际数据分析：
- 货品ID `624275971816` → 主要商家编码: "印刷费" (出现112次)
- 货品ID `658752042894` → 主要商家编码: "印刷费" (出现422次)  
- 货品ID `790611906472` → 主要商家编码: "真不错白色蛋糕保温袋6寸双层1个" (出现4645次)

### 📊 业务价值

**商家编码的作用**：
- **库存管理**: 通过商家编码快速定位具体商品规格
- **订单处理**: 便于订单明细匹配和库存扣减
- **数据分析**: 分析不同商家编码的销售表现
- **供应链管理**: 追踪货品的供应商和规格信息

**用户体验提升**：
- 提供更详细的货品信息，辅助选择决策
- 商家编码帮助区分同一货品ID下的不同规格
- 数据展示更加完整和专业

## 2025-01-17 14:00

### 📊 平台货品销量统计功能

**功能概述**：
为平台货品选择弹窗增加销量字段，通过关联查询`lirun.订单明细`表统计每个货品ID的历史销量。

**技术实现**：

#### 1. 数据库关联查询
- **主表**: `lirun.平台货品id` - 获取货品基本信息
- **关联表**: `lirun.订单明细` - 统计销量数据
- **关联字段**: `平台货品id.货品ID` = `订单明细.平台货品ID`
- **聚合统计**: `SUM(订单明细.数量)` 计算总销量

#### 2. 后端数据模型更新
- **PlatformProductVO**: 新增`salesVolume`字段
- **Mapper SQL**: 使用LEFT JOIN关联查询销量
- **空值处理**: 使用`COALESCE(s.salesVolume, 0)`处理无销量记录

#### 3. 前端界面优化
- **新增销量列**: 表格新增"销量"列，右对齐显示
- **数据样式**: 销量数字使用绿色高亮显示，突出重要信息
- **列宽调整**: 货品ID和店铺名称列宽各调整为200px，销量列100px

#### 4. 查询性能优化
- **子查询优化**: 销量统计使用子查询预聚合，提高查询效率
- **索引利用**: 利用`平台货品ID`字段的索引加速关联查询
- **条件过滤**: 过滤空值和无效数据，确保统计准确性

### 📈 数据统计说明

**销量统计规则**：
- **统计范围**: 所有历史订单明细数据
- **统计指标**: 按货品ID汇总的订单数量总和
- **数据来源**: `lirun.订单明细`表中的`数量`字段
- **默认值**: 无销量记录的货品显示为0

**显示效果**：
- 销量数字使用绿色字体，增强视觉效果
- 支持按销量排序和筛选
- 帮助用户选择热销货品

### 🎯 业务价值

- **销量透明**: 直观显示各货品的历史销量数据
- **选择依据**: 为用户选择货品提供数据支撑
- **热销识别**: 快速识别热销和滞销货品
- **决策支持**: 基于真实销量数据做出业务决策

## 2025-01-17 12:00

### 🛍️ 链接视角平台货品选择功能实现

**功能概述**：
为链接视角页面实现从`lirun`数据库`平台货品id`表读取真实数据的商品选择功能，替换原有的模拟数据。

**技术实现**：

#### 1. 后端平台货品管理模块
- **实体类**: `PlatformProductEntity` - 对应`lirun.平台货品id`表
- **VO类**: `PlatformProductVO` - 前端展示数据格式
- **查询表单**: `PlatformProductQueryForm` - 支持搜索和分页
- **DAO层**: `PlatformProductDao` - 数据库操作接口
- **Service层**: `PlatformProductService` - 业务逻辑处理
- **Controller层**: `PlatformProductController` - API接口提供

#### 2. 数据库操作
- **Mapper XML**: `PlatformProductMapper.xml` - 跨数据库查询`lirun.平台货品id`
- **分页查询**: 支持按货品ID和店铺名称搜索的分页查询
- **店铺列表**: 获取所有不重复的店铺名称列表
- **排序**: 按店铺名称和货品ID排序

#### 3. 前端界面更新
- **API接口**: `platform-product-api.js` - 平台货品相关API调用
- **商品选择弹窗**: 更新`product-select-modal.vue`组件
  - 表格列调整：货品ID、店铺名称、显示名称
  - 查询条件：搜索关键词、店铺下拉选择
  - 店铺筛选：支持搜索和选择特定店铺
- **链接视角页面**: 更新标签文字为"平台货品"，优化显示效果

#### 4. 数据展示优化
- **货品ID**: 使用等宽字体显示，便于识别
- **显示名称**: 格式为"货品ID - 店铺名称"
- **店铺选择**: 支持搜索功能的下拉选择框
- **选中提示**: 显示已选择的平台货品数量和名称

### 📊 数据来源

**平台货品ID表统计**：
- **总记录数**: 8,531条平台货品
- **店铺数量**: 37个不同店铺
- **主要店铺**: 小红书包装妹的店(1,128个)、天猫穆铭旗舰店(464个)等
- **数据特点**: 货品ID唯一，每个货品对应一个店铺

### 🔧 接口设计

**API接口**：
- `POST /platform/product/queryPage` - 分页查询平台货品
- `POST /platform/product/getAllShopNames` - 获取所有店铺名称

**查询参数**：
- `searchWord`: 搜索关键词(货品ID或店铺名称)
- `shopName`: 指定店铺名称筛选
- `pageNum`, `pageSize`: 分页参数

### 🎯 业务价值

- **真实数据**: 使用真实的平台货品数据，替代模拟数据
- **精确查询**: 支持按货品ID和店铺名称精确搜索
- **用户体验**: 直观的表格展示和便捷的选择功能
- **数据整合**: 将外部lirun数据库与SmartAdmin系统有效整合

### 🔍 使用说明

1. **选择平台货品**: 在链接视角页面点击"平台货品"输入框
2. **搜索筛选**: 输入货品ID或店铺名称进行搜索
3. **店铺选择**: 从下拉框选择特定店铺筛选
4. **批量选择**: 支持多选，勾选所需的平台货品
5. **确认选择**: 点击确定完成选择，返回主页面

## 2025-01-17 10:30

### 🔒 客户视角和链接视角操作日志功能实现

**功能概述**：
为客户视角和链接视角页面的所有操作添加完整的操作日志记录功能，确保用户的每一个操作都能被追踪和审计。

**实现内容**：

#### 1. 客户视角操作日志
- **控制器增强**: 为`CustomerViewController`添加`@OperateLog`注解
- **操作记录范围**:
  - 分页查询客户视角数据
  - 查询客户详情信息
  - 导出客户数据到Excel
  - 获取客户统计信息
- **日志内容**: 记录操作人、操作时间、请求参数、IP地址、用户代理等详细信息

#### 2. 链接视角操作日志
- **控制器创建**: 新建`CustomerLinkController`并添加`@OperateLog`注解
- **API接口实现**:
  - `POST /api/customer/link/queryPage` - 分页查询链接数据
  - `GET /api/customer/link/detail/{customerCode}` - 查看链接详情
  - `POST /api/customer/link/manage` - 管理链接关系
  - `GET /api/customer/link/statistics/{customerCode}` - 获取链接统计
  - `POST /api/customer/link/exportExcel` - 导出链接数据
  - `GET /api/customer/link/network/{customerCode}` - 获取客户关联网络
- **权限控制**: 每个接口都配置了相应的权限验证注解

#### 3. 客户跟进记录操作日志
- **控制器增强**: 为`CustomerRecordController`添加`@OperateLog`注解和权限控制
- **操作记录范围**:
  - 分页查询客户跟进记录 (`customer:record:view`)
  - 添加客户跟进记录 (`customer:record:add`)
  - 更新客户跟进记录 (`customer:record:edit`)
  - 删除客户跟进记录 (`customer:record:delete`)

#### 4. 客户订单操作日志
- **控制器增强**: 为`CustomerOrderController`添加`@OperateLog`注解和权限控制
- **操作记录范围**:
  - 分页查询客户订单明细 (`customer:order:view`)
  - 根据客户编码查询订单明细 (`customer:order:view`)

### 🔧 技术实现特点

- **统一注解**: 使用SmartAdmin内置的`@OperateLog`注解，自动记录操作日志
- **权限集成**: 结合`@SaCheckPermission`注解，确保操作权限验证
- **异步处理**: 操作日志记录采用线程池异步处理，不影响业务性能
- **完整信息**: 记录操作人、操作模块、操作内容、请求参数、IP地址等完整信息
- **失败记录**: 自动捕获异常并记录操作失败原因

### 📊 操作日志记录内容

每个操作都会记录以下信息：
- **操作人信息**: 用户ID、用户名、用户类型
- **操作详情**: 操作模块、操作内容、请求URL、请求方法
- **请求信息**: 请求参数、IP地址、IP地区、User-Agent
- **执行结果**: 成功/失败状态、失败原因（如有）
- **时间信息**: 操作时间、创建时间、更新时间

### 🎯 业务价值

- **合规审计**: 满足数据操作的审计要求，所有客户数据操作都有记录
- **安全监控**: 可以追踪异常操作，发现潜在的安全风险
- **问题排查**: 通过操作日志快速定位问题发生的时间和操作人
- **行为分析**: 分析用户操作习惯，优化系统功能和界面设计

### 🔍 查看操作日志

用户可以通过以下方式查看操作日志：
- **系统管理** → **操作日志** → **分页查询**：查看所有用户的操作记录
- **个人中心** → **我的操作日志**：查看当前登录用户的操作记录
- **日志详情**：点击具体日志记录查看详细的操作信息

## 2025-06-17 22:20

### 🐛 客户视角Controller路径映射修复

**问题描述**：
- 客户视角页面报错：`No static resource api/customer/view/queryPage`
- 修复前端API路径后，前后端路径不匹配问题
- 前端调用：`/api/customer/view/queryPage`，后端Controller：`/customer/view/queryPage`

**修复内容**：
**修复`CustomerViewController.java`**：
- 所有Controller路径映射添加`/api`前缀
- `@PostMapping("/customer/view/queryPage")` → `@PostMapping("/api/customer/view/queryPage")`
- `@GetMapping("/customer/view/detail/{customerUniqueCode}")` → `@GetMapping("/api/customer/view/detail/{customerUniqueCode}")`
- `@PostMapping("/customer/view/export")` → `@PostMapping("/api/customer/view/export")`
- `@GetMapping("/customer/view/statistics")` → `@GetMapping("/api/customer/view/statistics")`

**验证结果**：
- ✅ 前后端API路径完全一致
- ✅ 客户视角功能应该可以正常访问
- ✅ 项目编译成功

**说明**：
- `CustomerRecordController`已经正确使用`@RequestMapping("/api/customer/record")`
- `CustomerOrderController`已经在前面修复，使用独立的`@PostMapping("/api/customer/order/page/query")`
- 只有`CustomerViewController`需要添加`/api`前缀

## 2025-06-17 22:15

### 🐛 前端API路径错误修复

**问题描述**：
- 客户订单明细页面查询报错：`No static resource customer/order/page/query`
- Spring Boot将API请求当作静态资源处理，导致404错误
- 错误原因：前端API调用缺少`/api`前缀

**修复内容**：
1. **修复`customer-order-api.js`**：
   - 所有API路径添加`/api`前缀
   - `/customer/order/page/query` → `/api/customer/order/page/query`
   - `/customer/order/customer/{customerCode}` → `/api/customer/order/customer/{customerCode}`
   - `/customer/order/detail/{orderId}` → `/api/customer/order/detail/{orderId}`
   - `/customer/order/export` → `/api/customer/order/export`

2. **修复`customer-view-api.js`**：
   - 所有API路径添加`/api`前缀
   - `/customer/view/queryPage` → `/api/customer/view/queryPage`
   - `/customer/view/detail/{customerId}` → `/api/customer/view/detail/{customerId}`
   - `/customer/view/export` → `/api/customer/view/export`
   - `/customer/view/statistics` → `/api/customer/view/statistics`

3. **修复`customer-link-api.js`**：
   - 所有API路径添加`/api`前缀
   - `/customer/link/queryPage` → `/api/customer/link/queryPage`
   - 其他所有链接相关API路径

**验证结果**：
- ✅ API路径与后端Controller映射一致
- ✅ 解决Spring Boot静态资源路由冲突问题
- ✅ 确保所有客户相关功能正常访问

**技术说明**：
- Spring Boot默认将不匹配Controller的路径当作静态资源处理
- 后端Controller使用`@PostMapping("/api/customer/order/page/query")`
- 前端必须使用完整的API路径包含`/api`前缀

## 2025-06-17 22:00

### 🐛 LocalDate字段编译错误修复

**问题描述**：
- `LirunJdbcService.java`第238行出现编译错误：`java: 找不到符号 - 方法 trim(), 位置: 类 java.time.LocalDate`
- `LocalDate`类型没有`trim()`方法，该方法只存在于字符串类型

**修复内容**：
1. **修复`queryCustomerViewPage`方法**：
   - 移除`queryForm.getLastPurchaseDateBegin().trim().isEmpty()`中的`trim()`调用
   - 改为简单的`null`检查：`queryForm.getLastPurchaseDateBegin() != null`
   - 同样修复`lastPurchaseDateEnd`字段的检查逻辑

2. **修复`exportCustomerViewQuery`方法**：
   - 移除对`LocalDate`字段的`trim()`方法调用
   - 保持对字符串字段（如`customerUniqueCode`、`latestOrderNo`等）的`trim()`检查

**技术细节**：
- `CustomerViewQueryForm`中的日期字段`lastPurchaseDateBegin`和`lastPurchaseDateEnd`是`LocalDate`类型
- `CustomerOrderQueryForm`中的日期字段`paymentTimeBegin`和`paymentTimeEnd`是`String`类型，可以正常使用`trim()`
- 修复后保持了原有的业务逻辑，仅修正了类型检查方式

**验证结果**：
- ✅ 项目编译成功，无编译错误
- ✅ 所有警告均为非关键性的Lombok代码生成提示
- ✅ 业务逻辑保持一致，仅修正了类型安全问题

## 2025-06-17 15:50

### 🔧 数据库连接配置更新

- **数据库迁移**: 将数据库连接从原服务器迁移到阿里云RDS数据库
- **新数据库信息**:
  - **主机**: `rm-bp19i4ff32kmp4q65ro.mysql.rds.aliyuncs.com`
  - **端口**: `3306`
  - **用户名**: `sa`
  - **密码**: `hhysbyj1234!`
- **配置文件更新**:
  - 开发环境: `sa-base/src/main/resources/dev/sa-base.yaml`
  - 生产环境: `sa-base/src/main/resources/prod/sa-base.yaml`
- **数据库统一**: 主数据库(`smart_admin_v3`)和lirun数据库都使用相同的RDS服务器
- **连接优化**: 使用阿里云RDS提供更稳定的数据库服务和更好的性能

### 🚀 技术改进要点

- **高可用性**: 阿里云RDS提供自动备份、故障转移等高可用功能
- **性能优化**: RDS提供更好的网络连接和数据库性能
- **安全性**: 统一的数据库访问控制和安全策略
- **维护便利**: 简化数据库维护和监控工作
- **成本优化**: 统一的数据库服务降低运维成本

### 📊 配置变更详情

**开发环境变更**:
- 主数据库URL: 更新为阿里云RDS地址
- Lirun数据库URL: 更新为阿里云RDS地址
- 认证信息: 统一使用新的用户名和密码

**生产环境变更**:
- 主数据库URL: 更新为阿里云RDS地址
- Lirun数据库URL: 更新为阿里云RDS地址
- 认证信息: 统一使用新的用户名和密码
- 保持生产环境特有的性能和安全配置

## 2025-06-17 15:47

### 🔄 客户视角数据源切换到lirun数据库

- **数据源迁移**: 将客户视角页面的数据查询从默认的`smart_admin_v3`数据库切换到`lirun`数据库
- **服务层重构**: 修改`CustomerViewService`，所有查询方法现在通过`LirunJdbcService`从lirun数据库获取数据
- **新增JDBC方法**: 在`LirunJdbcService`中添加了以下客户视角相关方法：
  - `queryCustomerViewPage()` - 分页查询客户视角数据
  - `getCustomerViewDetail()` - 查询客户详情
  - `exportCustomerViewQuery()` - 导出客户数据
  - `getCustomerViewStatistics()` - 获取客户统计信息
- **数据表映射**: 从lirun数据库的`crm_客户查询`表查询客户视角数据
- **向下兼容**: 保留原有的从默认数据库查询的方法作为备用（方法名添加`FromDefault`后缀）

### 🔧 技术实现要点

- **直接JDBC连接**: 使用JDBC直接连接lirun数据库，避免多数据源配置复杂性
- **配置复用**: 复用现有的`lirun.datasource`配置项
- **查询条件支持**: 支持客户编码、订单号、店铺名称、购买日期等多维度查询
- **分页查询**: 实现了完整的分页查询逻辑，包括总记录数统计
- **异常处理**: 完善的SQL异常处理和错误日志记录
- **数据映射**: 正确映射数据库字段到VO对象，支持日期类型转换

### 📊 功能影响

- **客户视角页面**: 现在显示的是lirun数据库中的实时客户数据
- **查询性能**: 直接查询lirun数据库，减少数据同步延迟
- **数据一致性**: 确保客户视角数据与订单明细数据来源一致
- **统计准确性**: 统计信息基于lirun数据库的实时数据计算

## 2025-06-17

### 🔧 外部数据库连接权限问题修复

- **问题识别**: 生产环境部署时，SmartAdmin无法连接外部lirun数据库(**************:3306)
- **根本原因**: 腾讯云服务器IP(***************)未被授权访问外部数据库
- **解决方案**: 
  1. 在外部数据库服务器上为腾讯云IP添加访问权限
  2. 修改数据库用户权限，允许从腾讯云IP连接
  3. 检查防火墙和安全组设置
- **配置优化**: 
  - 将硬编码的数据库连接信息改为配置文件形式
  - 在生产环境配置中添加lirun数据库连接参数
  - 提高配置的灵活性和可维护性

### 🚀 技术改进要点

- **网络连通性测试**: 使用`telnet 47.116.************`测试网络连通性
- **权限管理**: 数据库用户权限精确配置，只允许必要的IP访问
- **错误处理**: 改进错误返回机制，避免返回code 10001但HTTP状态200的混淆情况
- **监控告警**: 建议添加数据库连接状态监控，及时发现连接问题

## 2025-06-16

### 🔧 客户视角权限控制完善

- **权限验证机制修复**: 修正了SmartAdmin的Sa-Token权限验证机制中`api_perms`字段与Controller注解不匹配的问题
- **数据库权限配置更新**: 执行SQL更新，将`t_menu`表中的`api_perms`字段值修改为与Controller注解一致
- **前端权限控制增强**: 在客户跟进记录模态框中添加`v-privilege`指令，控制编辑、删除和添加按钮的显示
- **技术突破**: 深入理解了SmartAdmin权限机制的工作原理，为后续功能开发奠定基础

### 🎯 权限配置修复详情

```sql
-- 修复权限验证字段
UPDATE t_menu SET api_perms = 'customer:view:query' WHERE web_perms = 'customer:view:query';
UPDATE t_menu SET api_perms = 'customer:view:detail' WHERE web_perms = 'customer:view:detail';
UPDATE t_menu SET api_perms = 'customer:view:export' WHERE web_perms = 'customer:view:export';
UPDATE t_menu SET api_perms = 'customer:record:manage' WHERE web_perms = 'customer:record:manage';
```

### 📝 前端权限控制

- 编辑/删除按钮: `v-privilege="'customer:record:manage'"`
- 保存/更新按钮: `v-privilege="'customer:record:manage'"`
- 添加跟进记录标签: `v-privilege="'customer:record:manage'"`

## 2025-06-15

### ✨ 客户视角功能新增

- **新增客户视角页面**: 提供客户数据的查询、详情查看、导出和统计功能
- **菜单权限配置**: 完整的菜单结构和权限控制体系
- **数据展示优化**: 客户编码截断显示、销售额高亮、毛利率标签化显示
- **交互功能完善**: 客户详情模态框、统计信息展示、Excel导出功能

### 🔧 技术实现

- **后端架构**: Controller + Service + DAO + Mapper 完整分层架构
- **前端组件**: Vue3 + Ant Design Vue 响应式设计
- **权限控制**: 基于Sa-Token的细粒度权限验证
- **数据处理**: MyBatis-Plus分页查询和Excel导出

### 📊 功能特性

- 多维度查询: 客户编码、订单号、店铺、日期范围等
- 数据统计: 总客户数、正常客户、已流失客户统计
- 导出功能: 支持Excel格式数据导出
- 响应式设计: 适配不同屏幕尺寸的设备

## 2025-06-17

### 🔧 外部数据库连接优化

- **数据库连接配置化**: 移除 `LirunJdbcService` 中硬编码的数据库连接信息（URL、用户名、密码）。
- **动态配置加载**: 改为使用 Spring 的 `@Value` 注解，从 `application.yaml` 系列配置文件中动态加载 `lirun` 数据库的连接信息。
- **环境隔离**: 分别在开发环境 (`dev/sa-base.yaml`) 和生产环境 (`prod/sa-base.yaml`) 的配置文件中添加了 `lirun.datasource` 配置项。
- **提升系统灵活性与安全性**: 此项重构使得不同环境可以连接到不同的外部数据库，避免了在代码中暴露敏感信息，提升了系统的可维护性和安全性。

## 2025-06-16

### 🔧 权限机制深度优化与问题解决

- **权限验证机制排查**: 完成SmartAdmin Sa-Token权限框架深度技术排查，发现权限验证使用`api_perms`字段而非`web_perms`字段的关键机制
- **客户视角权限修复**: 通过修改数据库权限标识，将`api_perms`字段值统一为与Controller注解一致的权限标识，彻底解决"吕枫"用户访问客户视角页面的权限验证失败问题
- **权限标识统一**: 执行SQL更新，将客户视角相关权限的`api_perms`字段从接口路径格式改为权限标识格式（如`customer:view:query`）
- **技术文档完善**: 在项目笔记中新增"SmartAdmin权限验证机制深度排查与解决经验总结"章节，详细记录排查过程、根本原因、解决方案和最佳实践
- **排查方法论建立**: 总结了从现象到本质的权限问题排查方法论，建立了追踪代码路径、对比数据库与代码、理解框架机制的系统化排查流程
- **前端权限控制完善**: 修复客户跟进记录功能中缺失的权限控制，为"添加跟进记录"、"修改"、"删除"、"保存记录"等操作按钮添加`v-privilege="'customer:record:manage'"`权限指令
- **细粒度权限重构**: 创建完整的客户跟进记录细粒度权限SQL脚本，将粗粒度的"管理跟进记录"权限拆分为：
  - `customer:record:view` - 查看跟进记录
  - `customer:record:add` - 添加跟进记录
  - `customer:record:edit` - 修改跟进记录
  - `customer:record:delete` - 删除跟进记录
  - `customer:order:view` - 查看订单明细
- **前端权限指令优化**: 根据新的细粒度权限，更新所有相关前端组件的`v-privilege`指令，实现精确的权限控制

### 📚 开发经验积累

- **权限框架理解**: 深入理解SmartAdmin的Sa-Token权限验证机制，明确权限加载和验证的完整链路
- **问题定位技能**: 掌握复杂权限问题的系统化排查方法，从数据库配置到代码实现的全链路分析
- **最佳实践制定**: 制定权限标识规范化、权限测试流程等避免类似问题的开发规范
- **前端权限控制规范**: 建立前端权限控制的检查清单，确保所有操作按钮都有对应的权限控制指令
- **细粒度权限设计**: 掌握了权限粒度设计的最佳实践，在安全性和易用性之间找到平衡点

## 2024-08-01

### ⚙️ 权限功能修复

- **修复SQL脚本错误**: 修正了`t_role_menu`表因缺少`create_user_id`和`update_user_id`字段而导致的SQL执行失败问题。
- **新增菜单与权限**: 成功添加"客户视角"菜单及其相关的查询、详情、导出、跟进记录管理等功能权限点。

## 2025-01-15 21:15

### 🎯 客户视角页面数字字段居中优化

#### 表格字段居中显示
- **购买次数字段居中**: 客户视角表格中购买次数列数字居中显示，提升数据可读性
- **复购周期字段居中**: 复购周期(天)列数字居中显示，保持数字类字段的一致性
- **距今天数字段居中**: 距今天数列数字居中显示，便于数据对比分析
- **购买天数字段居中**: 购买天数列数字居中显示，统一数字类字段的视觉效果
- **表格布局统一**: 所有数字类字段均采用居中对齐，提升整体表格的专业性和可读性

## 2025-01-15 21:00

### 🎯 订单明细功能完善

#### 自动查询功能
- **标签页切换自动查询**: 点击"订单明细"标签页时自动触发查询，获取当前客户的所有订单数据
- **智能数据加载**: 切换到订单明细时根据客户编码自动调用`queryOrders()`函数
- **用户体验优化**: 无需手动点击查询按钮，直接显示客户相关订单信息

#### 字段显示优化
- **已付字段居中**: 订单明细表格中已付字段数字完全居中显示，视觉更加整齐
- **CSS样式精确定位**: 使用`.orders-section .record-table`精确定位订单明细表格，避免影响其他表格
- **表格布局改进**: 确保已付字段在订单明细表格中的第4列正确居中对齐

## 2025-01-15 20:30

### 🎯 订单明细显示优化

#### 字段显示调整
- **已付字段优化**: 移除已付字段前面的人民币符号(¥)，数字居中显示，保持绿色加粗样式
- **移除订单金额字段**: 根据业务需求，从订单明细表格中完全移除"订单金额"列
- **表格列精简**: 保留原始单号、商家编码、数量、已付、付款时间等核心字段
- **数据清洁**: 已付字段仅显示数值，去除货币符号，更加简洁明了

#### 数据库隔离确认
- **数据库使用范围**: 确认订单明细功能专用lirun数据库，其他功能不受影响
- **LirunJdbcService专用性**: 该服务仅用于订单明细查询，不影响其他业务模块的数据源
- **数据隔离保证**: 客户跟进记录、客户视角等其他功能继续使用默认数据源

## 2025-01-15

### 🎨 链接视角页面布局优化

#### 移除多余字符和紧凑布局
- **清理多余字符**: 移除了页面中显示的多余`\n\n`字符，确保界面干净整洁
- **紧凑化布局**: 大幅减少各个模块之间的间距，让内容更加紧凑
- **优化卡片间距**: 将分析条件、指标卡片、图表区域的间距从24px减少到16px
- **精简内边距**: 调整卡片内部padding，从20px减少到16px，整体更紧凑

#### 细节优化调整
- **指标卡片优化**: 数值字体从28px调整为24px，减少内部间距
- **环形图表优化**: 圆形进度图从120px缩小到100px，减少占用空间
- **柱状图调整**: 高度从250px调整为220px，保持功能性的同时节省空间
- **文字大小调整**: 标题字体从16px调整为15px，标签字体适当缩小
- **间距统一**: 所有模块间距统一调整，保持视觉一致性

### 🎯 链接视角页面功能简化

#### 移除表格展示功能
- **完全移除表格**: 根据用户需求，移除了客户链接数据的表格展示功能
- **简化页面结构**: 页面现在专注于统计分析功能，不再显示详细的数据列表
- **优化用户体验**: 用户直接通过筛选条件进行统计分析，无需查看中间数据
- **空状态提示**: 未选择商品或未进行统计分析时，显示友好的空状态提示

#### 功能流程优化
- **直接分析模式**: 用户选择商品和筛选条件后，直接点击统计分析查看结果
- **移除数据查询**: 不再需要查询和展示中间的客户链接数据
- **简化代码结构**: 移除了表格相关的所有代码，包括列定义、数据查询、分页等
- **专注统计功能**: 页面现在完全专注于复购分析和统计展示

### 🔧 链接视角页面问题修复

#### 默认数据显示优化
- **移除默认表格数据**: 页面初始化时不再显示模拟的表格数据
- **条件化数据显示**: 只有在用户选择商品后才显示相关的链接数据
- **自动刷新机制**: 选择商品后自动刷新表格数据，重置条件时清空数据
- **状态同步**: 重置查询条件时同时隐藏统计分析面板，确保界面状态一致

#### 柱状图显示修复
- **容器高度调整**: 将柱状图容器高度从200px增加到250px，确保有足够显示空间
- **Y轴范围优化**: 设置Y轴最大值为数据最大值的1.2倍，防止柱子超出容器范围
- **网格布局调整**: 增加图表的内边距（left: 10%, right: 10%, bottom: 15%, top: 15%）
- **柱子宽度优化**: 将柱子宽度从60%调整为50%，提供更好的视觉效果
- **标签字体设置**: 为柱状图顶部数值标签设置12px字体大小

### 🎯 链接视角页面UI增强

#### 付款日期筛选功能
- **新增筛选条件**: 在链接视角页面查询表单中添加付款日期范围选择器
- **组件配置**: 使用`a-range-picker`支持日期范围筛选，配置预设时间范围选择
- **一致性优化**: 与客户视角页面的上次购买日期组件保持完全一致的配置
- **预设时间**: 支持今日、昨日、本月、上个月、下个月、本年度、上年度等快速选择
- **数据绑定**: 付款日期范围分别绑定到`queryForm.paymentDateBegin`和`queryForm.paymentDateEnd`字段
- **布局优化**: 付款日期选择器宽度240px，与其他查询条件保持统一风格
- **变化处理**: 添加`onChangePaymentDate`函数处理日期范围变化事件
- **重置功能**: 重置查询条件时同时清空付款日期选择
- **用户体验**: 支持快速选择时间范围，便于分析特定时期的客户链接情况

#### 商品选择功能完整实现
- **选择商品入口**: 在查询表单中添加商品选择输入框，点击触发商品选择弹窗
- **视觉设计**: 使用只读输入框配合选择图标，明确提示用户点击选择
- **状态显示**: 
  - 未选择时显示"点击选择商品"提示文字
  - 选择单个商品时显示商品名称
  - 选择多个商品时显示"已选择 X 个商品"统计信息
- **数据绑定**: 选中商品ID列表绑定到`queryForm.selectedProductIds`字段

#### 统计分析功能重构 - 内嵌式设计
- **设计理念调整**: 将统计分析从弹窗模式改为内嵌式面板，直接在当前页面展示分析结果
- **统计分析入口**: 在表格操作行左侧添加"统计分析"按钮，使用柱状图图标
- **选择商品验证**: 点击统计分析时检查是否已选择商品，未选择时提示用户先选择商品
- **内嵌面板设计**: 统计分析面板直接显示在表格操作行下方，无需弹窗操作
- **收起功能**: 面板右上角提供"收起"按钮，用户可随时隐藏统计分析面板
- **核心指标卡片**: 4个关键指标卡片展示
  - 平均复购周期(12.75天)：主要指标，使用大字号突出显示
  - 最小复购周期(1天)、最大复购周期(24天)、平均复购间隔
  - 每个指标配备帮助提示，解释指标含义
- **复购率分析**: 
  - 环形进度图显示复购率百分比(92.0%)
  - 使用CSS conic-gradient实现动态环形进度条
  - 侧边统计显示复购人数(23人)和单次购买人数(2人)
  - 不同颜色圆点区分复购类型
- **复购次数分布**: 使用ECharts柱状图展示第1次、第2次复购分布
  - 支持鼠标悬停提示详细数据
  - 柱状图顶部显示具体数值
  - 不同颜色区分不同复购次数
- **复购明细表格**: 
  - 显示复购次数、复购人数、复购件数、复购金额、平均复购周期
  - 根据数值大小使用不同颜色标识
  - 包含均值和合计行
  - 右下角提供"下载明细"功能入口
- **分析条件展示**: 在面板顶部显示当前的分析条件，包括客户编码、链接状态、付款日期和选择的商品
- **条件高亮显示**: 选择的商品信息以蓝色高亮显示，突出分析重点
- **动态数据生成**: 根据选择的商品数量动态调整统计指标和复购数据
- **数据关联性**: 选择商品越多，复购周期、客户数量等指标相应增加，体现商品组合的影响
- **柱状图数据同步**: ECharts图表数据与表格数据保持同步，鼠标悬停显示详细信息
- **内嵌式布局**: 统计面板完美融入页面布局，不影响用户的正常操作流程
- **样式优化**: 使用渐变背景、悬停效果、圆角边框等现代化UI设计
- **资源管理**: 面板隐藏和组件销毁时自动释放ECharts资源，避免内存泄漏
- **用户体验**: 无弹窗干扰，统计数据与表格数据在同一视图中展示，便于对比分析

#### 商品选择弹窗组件
- **弹窗结构**: 创建`ProductSelectModal`组件，宽度900px，支持模态框操作
- **查询功能**: 
  - 商品名称搜索（支持商品名称和商品编号搜索）
  - 商品分类筛选（塑料制品、包装材料、纸质制品）
  - 商品状态筛选（在售、停售）
  - 支持Enter键快速搜索
- **表格展示**:
  - 商品信息列：显示商品图片、名称、编号的综合信息
  - 分类、价格、库存、状态等核心字段
  - 表格高度300px，支持垂直滚动
  - 商品图片40x40px圆角显示，加载失败自动隐藏
- **多选功能**:
  - 支持复选框多选商品
  - 已选择商品提示条，显示选择数量和清空操作
  - 停售商品自动禁用，无法选择
  - 支持跨页保持选择状态
- **数据处理**:
  - 模拟商品数据包含5种不同类别商品
  - 支持按条件实时筛选和分页
  - 价格显示为红色货币格式
  - 状态使用彩色标签（绿色在售/红色停售）
- **分页组件**: 支持10/20/50条每页，快速跳转，总数统计
- **操作控制**: 确认选择和取消操作，支持重置表单状态

#### 表格结构优化
- **新增字段**: 在链接视角表格中添加"付款日期"和"关联商品"列
- **列宽调整**: 付款日期120px，关联商品150px，保持表格平衡
- **数据展示**: 模拟数据包含付款日期和关联商品信息，便于功能验证
- **布局优化**: 操作列固定在右侧，保持120px宽度

#### 交互体验提升
- **重置功能**: 重置查询条件时同时清空已选择的商品
- **提示信息**: 选择商品后显示成功提示，包含选择数量
- **图标使用**: 导入`SelectOutlined`图标，提供清晰的选择提示
- **响应式**: 组件支持响应式布局，适配不同屏幕尺寸

## 2025-01-15 (早期更新)

### 🎨 UI优化改进

#### 链接视角菜单创建
- **新增菜单**: 在客户管理目录下创建"链接视角"菜单，提供客户关系链接分析功能
- **页面结构**: 创建完整的Vue页面结构，包含查询表单、数据表格、分页组件
- **表格设计**: 设计链接强度、链接状态、关联客户数、互动频次等核心字段
- **权限配置**: 配置查询、详情查看、关系管理等功能权限点
- **API接口**: 创建完整的API接口文件，定义分页查询、详情查看、关系管理等接口
- **数据库脚本**: 提供完整的菜单数据SQL脚本，包含主菜单和权限点
- **视觉效果**: 链接强度显示为进度条，链接状态使用彩色标签，增强数据可视化
- **模拟数据**: 提供示例数据展示，便于前端开发和测试

#### 客户视角页面操作简化
- **移除查看功能**: 删除操作栏中的"查看"按钮，简化界面操作流程
- **代码清理**: 移除相关的`showCustomerDetail`函数和`CustomerViewDetailModal`组件引用
- **操作列优化**: 将操作列宽度从90px调整为60px，节省表格空间
- **按钮文案优化**: 将操作按钮从"记录"改为"跟进"，更符合CRM业务场景和专业术语
- **界面精简**: 保留核心的客户跟进功能，聚焦于客户跟进记录管理
- **性能优化**: 减少不必要的组件加载，提升页面性能

#### 订单明细页面布局优化
- **表格列调整**: 根据用户需求移除"订单编号"、"订单状态"、"客服备注"列，保留核心业务数据
- **新增业务字段**: 在"商家编码"后新增"数量"和"已付"字段，提供更完整的订单财务信息
- **列宽度优化**: 重新分配列宽度，原始单号200px、商家编码150px、数量100px、已付120px
- **表格高度增加**: 将表格容器高度从160px增加到200px，可显示更多订单数据行
- **滚动配置调整**: 根据新的列配置调整横向滚动宽度为850px，优化显示效果
- **金额显示优化**: "已付"字段显示为绿色加粗，"订单金额"字段显示为蓝色加粗，便于区分
- **代码优化**: 移除不再使用的订单状态相关处理逻辑和样式函数
- **布局精简**: 通过减少非核心列，让表格更加聚焦于重要的业务数据

### 🚀 客户视角页面功能增强

#### 新增财务数据字段显示
- **功能新增**: 在客户视角页面表格中添加总成本、总利润、总客户毛利率字段显示
- **位置调整**: 将新字段放置在总销售额字段的右侧，保持数据的逻辑关联性
- **数据类型**: 成本和利润显示为金额格式，毛利率直接显示数据库中的格式化数据
- **颜色逻辑**: 总利润和总客户毛利率为负数时显示绿色，突出显示亏损客户
- **后端优化**:
  - 更新`CustomerViewVO.java`，添加`总成本`、`总利润`、`总客户毛利率`字段
  - 修改`CustomerViewMapper.xml`的`base_columns`，包含新的数据库字段
  - 充分利用数据库中已存在的财务数据字段
- **前端优化**:
  - 在`customer-view.vue`的columns配置中添加三个新列
  - 设置合适的列宽度（120px）确保数据完整显示
  - 为新字段设置蓝色加粗样式，与总销售额保持一致的视觉效果
  - **修复**: 总客户毛利率字段直接显示数据库中的格式化数据，避免重复显示百分号
  - **颜色优化**: 总利润和总客户毛利率负数显示为绿色(#52c41a)，便于快速识别亏损客户
- **用户体验**: 
  - 客户财务数据一目了然，便于快速分析客户价值
  - 数据排列有序，总销售额、总成本、总利润、总客户毛利率形成完整的财务分析视图
  - 负数字段绿色显示，快速识别需要重点关注的亏损客户
  - 保持表格的可读性和专业性

#### 客户跟进记录界面优化
- **字段更新**: 在客户信息卡片中将"毛利率"字段更名为"总毛利率"，保持命名一致性
- **数据显示**: 将"总毛利"字段更名为"总利润"，与数据库字段保持一致
- **数据映射**: 优化客户信息数据映射逻辑，确保正确显示总利润和总毛利率数据
- **信息完整**: 客户信息卡片现在完整显示客户的财务状况（总销售额、总利润、总毛利率）

#### 客户跟进记录页面功能扩展
- **新增订单明细标签页**: 在客户跟进记录页面增加"订单明细"标签页
- **统一界面设计**: 在同一个模态框中整合客户信息、跟进记录和订单明细三大功能模块
- **订单明细功能**: 新增完整的订单明细查看模块
  - 支持订单编号、原始单号、商家编码、状态、付款时间、客服备注等信息展示
  - 表格高度优化为200px，适应跟进记录页面的整体布局
  - 支持分页查询，与跟进记录保持一致的交互体验
- **查询功能优化**: 
  - 简化查询条件，只保留付款时间范围查询
  - 使用日期选择器支持时间范围筛选
  - 一键重置查询条件
- **状态管理优化**:
  - 自动初始化客户编码到订单查询参数
  - 独立的订单数据状态管理
  - 完整的加载和错误处理机制
- **视觉效果统一**:
  - 状态标签彩色显示，与跟进记录保持一致
  - 金额字段蓝色加粗显示，便于财务分析
  - 查询条件布局与跟进记录页面完全一致
- **数据展示**:
  - 提供5条不同状态的模拟订单数据
  - 包含完整的订单生命周期状态展示
  - 客服备注信息完整显示
- **用户体验优化**:
  - 三个标签页切换流畅：添加跟进记录、查看跟进记录、订单明细
  - 保持客户信息卡片不变，用户可在同一界面查看完整客户档案
  - 响应式布局，适配不同屏幕尺寸

### 🔧 Docker部署配置完善

#### 文件上传功能完整解决方案
- **问题解决**: 彻底解决Docker环境下文件上传后无法访问的404问题
- **根本原因**: 
  - 后端配置的文件存储路径与nginx静态文件服务路径不匹配
  - nginx.conf中的`location /uploads/`配置的alias路径错误
  - docker-compose.yml中的volume挂载路径配置不当
- **完整解决方案**:
  - **后端配置优化**: 修改`sa-base.yaml`中的文件存储路径为`/usr/share/nginx/html/uploads/`
  - **环境变量配置**: 通过docker-compose.yml环境变量覆盖配置文件设置
  - **nginx配置修正**: 添加正确的`location /uploads/`静态文件服务配置
  - **容器挂载统一**: 确保app和nginx容器共享同一个文件目录
- **关键配置**:
  ```yaml
  # docker-compose.yml 环境变量
  - FILE_STORAGE_LOCAL_UPLOAD_PATH=/usr/share/nginx/html/uploads/
  - FILE_STORAGE_LOCAL_URL_PREFIX=/uploads/
  
  # nginx.conf 静态文件服务
  location /uploads/ {
      alias /usr/share/nginx/html/uploads/;
      expires 1y;
      add_header Cache-Control "public, immutable";
  }
  ```
- **文件存储位置**:
  - **主机存储**: `/opt/smartadmin/frontend/uploads/`
  - **容器内路径**: `/usr/share/nginx/html/uploads/`
  - **访问URL**: `http://服务器IP/uploads/文件路径`

#### 前端环境配置优化
- **标题配置**: 修改`.env.production`中的`VITE_APP_TITLE`为'枫格后台管理系统'
- **API配置**: 确认生产环境API地址配置为相对路径`/api`
- **构建部署**: 重新执行`npm run build:prod`生成生产环境文件

#### 部署文档完善
- **新增章节**: 在`腾讯云服务器部署.md`中添加"文件上传功能完整解决方案"章节
- **详细步骤**: 包含完整的配置修改步骤、验证命令和排错指南
- **成功要素**: 总结路径一致性、环境变量优先级等关键成功要素
- **排错命令**: 提供常用的Docker容器调试和文件检查命令

### 📋 项目管理规范

#### 更新日志管理
- **规范建立**: 每次代码更新完成后必须在更新日志.md文件记录新增功能点
- **文档结构**: 按日期组织，包含功能分类（UI优化、新增功能、技术架构等）
- **详细记录**: 记录问题现象、解决方案、关键配置和技术要点

## 2025-01-14

### 🎨 UI优化改进

#### 客户跟进记录页面布局优化
- **问题解决**: 修复查看跟进记录页面在数据量多时页面过长的问题
- **优化方案**:
  - 设置表格固定高度400px，内容区域可滚动
  - 将分页组件从表格内部移到表格外部，始终保持可见
  - 优化模态框整体高度为80vh，最大高度800px
  - 表格和分页组件使用small尺寸，提高空间利用率
- **布局改进**:
  - 表格区域使用flex布局，确保分页组件固定在底部
  - 添加`min-height: 0`确保flex子元素正确收缩
  - 分页组件背景色设为浅灰色，与表格区分
- **用户体验**: 无论数据多少，页面高度都保持固定，避免滚动操作

#### 表格显示问题修复
- **表格高度调整**: 修复查看跟进记录表格只显示1行的问题  
- **滚动条恢复**: 确保表格有固定高度120px，能显示约3行数据
- **查询条件优化**: 
  - 移除默认筛选条件，初始显示所有记录
  - 添加"全部类型"和"全部状态"选项
  - 修复记录筛选导致的显示不完整问题
- **分页布局完善**: 
  - 调整表格和分页组件的高度分配
  - 确保分页组件不遮挡表格内容
  - 分页组件固定在底部，高度50px
- **查询条件布局优化**:
  - 将查询条件移动到客户信息标题右侧
  - 移除"查询条件"标题，节省空间
  - 查询控件与客户信息标题同行显示
  - 为表格区域腾出更多显示空间
- **页面高度统一**:
  - 查询条件只在"查看跟进记录"页面显示
  - "添加跟进记录"页面不显示查询条件
  - 两个页面高度保持一致，提供统一的用户体验
  - 表单布局优化，操作按钮固定在底部

#### 极简风格色彩优化
- **整体色彩方案**: 采用现代极简风格，使用渐变背景和柔和色调
- **模态框设计**: 
  - 圆角增加到16px，更加现代化
  - 背景使用渐变色彩，增加层次感
  - 阴影效果更加柔和自然
- **客户信息卡片**:
  - 图标增加阴影效果和更丰富的渐变色彩
  - 信息卡片使用微妙的渐变背景
  - 悬停效果更加流畅，增加顶部光效
- **标签页设计**:
  - 标签按钮增加渐变背景和悬停动效
  - 活跃状态使用蓝色渐变，更加醒目
  - 圆角和阴影效果提升视觉层次
- **表格和表单**:
  - 查询区域使用卡片式设计，增加阴影
  - 按钮使用渐变色彩和悬停动效
  - 表格容器增加边框圆角和阴影效果
- **交互体验**:
  - 所有按钮增加悬停上浮效果
  - 颜色过渡更加平滑自然
  - 保持功能完整性的同时提升视觉美感

#### 客户跟进记录功能增强
- **默认显示**: 点击记录按钮默认显示查看跟进记录页面，提升用户体验
- **编辑功能**: 新增记录编辑功能，支持修改已有的跟进记录
  - 点击修改按钮自动填充表单数据
  - 动态显示"编辑跟进记录"或"添加跟进记录"标签
  - 保存按钮文本自适应显示"更新记录"或"保存记录"
- **删除功能**: 新增记录删除功能，支持逻辑删除跟进记录
  - 删除前弹出确认对话框，防止误操作
  - 删除成功后自动刷新列表
- **后端API完善**:
  - 新增`CustomerRecordUpdateForm`更新表单类
  - 实现`update`接口支持记录更新
  - 完善Controller和Service层的更新逻辑
- **表格优化**: 新增操作列，提供修改和删除按钮，操作更便捷

### 🚀 新增功能

#### 客户跟进记录管理
- **新增功能**: 为客户管理页面添加完整的跟进记录功能
- **功能特点**:
  - 📞 支持多种跟进类型：电话沟通、邮件联系、上门拜访、微信沟通、问题处理、其他
  - 📊 跟进状态管理：待跟进、跟进中、已完成、已取消
  - 📝 详细记录跟进内容和次数
  - 📋 历史记录查询和分页显示
  - 🎨 美观的UI设计，包含emoji图标和渐变按钮

#### 客户视角页面UI标准化
- **标准化内容**: 将客户视角页面布局调整为SmartAdmin标准布局
- **布局规范**:
  - 查询表单使用`smart-query-form`类，位于卡片外部
  - 卡片内包含：表格操作栏(右对齐)、数据表格、分页组件
  - 分页组件使用`smart-query-table-page`类
  - 移除复杂的固定头部滚动设计，采用标准`calcTableHeight`函数

### 📚 文档完善

#### SmartAdmin标准页面布局规范
- **新增文档**: 创建`SmartAdmin标准页面布局规范.md`
- **文档内容**:
  - 详细的页面布局标准和规范说明
  - 完整的代码模板和示例
  - CSS类名使用规范
  - 响应式设计指导原则
  - 最佳实践建议

### 🔧 技术改进

#### 代码结构优化
- **Vue 3 Composition API**: 全面使用Vue 3的组合式API
- **组件化设计**: 将复杂功能拆分为独立的组件模块
- **响应式布局**: 支持不同屏幕尺寸的自适应显示
- **用户体验**: 添加加载状态、动画效果、表单验证等交互优化

### 🎯 用户体验提升

#### 界面交互优化
- **按钮设计**: 使用渐变色和阴影效果，提升视觉层次
- **表单体验**: 优化表单布局和验证提示
- **响应式适配**: 针对不同设备尺寸进行界面适配
- **动画效果**: 添加淡入动画和悬停效果，提升操作反馈

---

## 技术栈信息
- **前端框架**: Vue 3 + Ant Design Vue
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **样式处理**: Less + CSS3

## 开发规范
- 遵循SmartAdmin标准页面布局规范
- 使用Vue 3 Composition API
- 组件化开发，提高代码复用性
- 响应式设计，支持多端适配

## 2025-01-15

### 🔥 UI重大升级

#### 客户跟进记录模态框UI大改版
- **设计风格**: 采用更紧凑、色彩更丰富的卡片式信息展示，提升视觉效果和信息密度
- **头部区域**: 优化了标题栏的高度，使其更加紧凑，字体大小调整为16px
- **客户信息区域**:
  - 采用带图标和背景色的信息单元格设计，每个信息点一目了然
  - 减小了整体高度，为下方表单和列表区域留出更多空间
  - 使用了8种不同的颜色和图标来区分不同的客户信息（客户编码、销售额、流失风险等）
  - 添加了鼠标悬停的浮动和阴影效果，增强交互体验
  - 背景色调整为#fafbfd，更显精致
- **目标**: 优化布局，力求在一个屏幕内展示所有核心信息，减少滚动操作

### 🚀 重大功能更新

#### 客户视角页面数据源切换
- **数据源迁移**: 将客户视角页面的数据源从原来的`t_customer`表切换到`crm_客户查询`表
- **后端改造**:
  - 修改`CustomerViewMapper.xml`，将所有SQL查询从`t_customer`表改为`crm_客户查询`表
  - 更新`CustomerViewVO.java`，使用中文字段名作为属性名，与数据库表结构完全一致
  - 调整`CustomerViewDao.java`、`CustomerViewService.java`、`CustomerViewController.java`的方法参数
  - 移除了数据库中不存在的字段（如总毛利、毛利率等）
- **前端适配**:
  - 调整表格列定义，使用中文字段名匹配数据库表结构
  - 修改表格的rowKey为"客户唯一编码"
  - 更新表格模板中的字段引用
  - 调整流失风险选项，增加"高风险"、"中风险"、"低风险"、"新客户"等选项
  - 添加日期格式化和流失风险颜色显示功能
- **数据库表结构对应**:
  - 客户唯一编码 (主键)
  - 总销售额、购买次数、复购周期
  - 首次成交日期、上次购买日期
  - 流失风险 (正常/高风险/中风险/低风险/已流失/新客户)
  - 上次购买后距今天数、购买天数
  - 最近一次下单店铺、最近下单原始单号
- **数据量**: 现在客户视角页面将直接从crm_客户查询表获取数据，包含156,281条客户记录

#### 客户视角页面查询优化
- **筛选条件顺序调整**: 将"上次购买日期"筛选条件移到"客户编码"前面
- **默认分页设置**: 将默认分页大小从10条改为50条
- **默认查询条件**: 页面打开时自动设置上次购买日期为近一个月（从一个月前到今天）
- **重置功能优化**: 重置查询条件时也会自动设置为近一个月的默认条件

### 🎨 UI界面优化

#### 客户视角页面显示优化
- **总销售额显示**: 去掉总销售额前面的人民币符号，保持数字的简洁显示

#### 客户跟进记录功能完善
- **客户信息部分功能实现**:
  - 实现客户信息卡片的数据显示功能
  - 所有文字采用居中对齐显示，提升视觉效果
  - 支持从数据库表字段名获取客户信息数据
  - 添加日期格式化功能，统一显示格式
- **查询条件标题优化**:
  - 为查看跟进记录的查询区域添加"查询条件"标题
  - 为每个查询文本框前面添加对应的条件标签
  - "跟进类型："和"跟进状态："标签，提升用户体验
  - 优化查询区域的布局和样式
- **界面样式优化**:
  - 去掉客户信息区域的紫色背景，改为白色背景的卡片
  - 去掉模态框头部的紫色渐变背景，改为白色背景
  - 移除客户信息区域的标题部分（"客户信息"标题和图标），保持内容简洁
  - 移除模态框头部的主标题，只保留关闭按钮，界面更加简洁
  - 调整模态框高度，确保保存和取消按钮能够直接看到
  - 设置内容区域最大高度为50vh，超出时可滚动
  - 优化关闭按钮的颜色和悬停效果
  - 优化整体视觉效果，提升用户体验

### 🔧 技术改进

#### 数据库字段映射优化
- **字段名统一**: 前后端统一使用中文字段名，与数据库表结构保持一致
- **数据类型适配**: 调整VO类的数据类型，确保与数据库字段类型匹配
- **API接口调整**: 修改详情查询接口参数类型，从Long customerId改为String customerUniqueCode

#### 代码结构优化
- **移除冗余字段**: 清理了数据库中不存在的字段引用
- **统计查询增强**: 更新统计查询，增加各种风险等级的统计数据
- **查询条件简化**: 移除deleted_flag相关的条件判断，简化查询逻辑

---

## 2025-01-14 (历史记录)

### 新增功能
- **客户视角页面UI优化**: 将客户视角页面的UI布局调整为与系统标准页面（如登录登出记录页面、任务发布页面）一致的风格
  - 查询表单移至卡片外部，使用 `smart-query-form` 样式
  - 卡片内部采用简洁的表格操作行布局
  - 分页组件放置在卡片内部底部
  - 保留所有原有功能（高级搜索、导出、统计、详情查看等）
  - 实现了标准的页面布局规范

- **客户跟进记录功能**: 新增客户跟进记录管理功能，支持对客户进行跟进记录和查看
  - 在客户视角页面操作列中新增"记录"按钮
  - 创建客户跟进记录模态框组件，包含以下功能：
    - **客户信息展示**: 显示客户编码、总销售额、流失风险等基本信息
    - **添加跟进记录**: 支持记录跟进类型、状态、时间、内容等详细信息
    - **查看跟进记录**: 提供记录列表查询、筛选和管理功能
    - **表单验证**: 完整的表单验证和用户体验优化
    - **响应式设计**: 支持不同屏幕尺寸的适配
  - 跟进类型包括：电话沟通、邮件联系、上门拜访、微信沟通、问题处理、其他
  - 跟进状态包括：待跟进、跟进中、已完成、已取消
  - 支持设置下次跟进时间，便于后续跟进管理
  - UI界面美观，用户体验良好，为后续API集成预留接口

### 界面优化
- **客户跟进记录弹窗美化**: 全面优化客户跟进记录功能的用户界面
  - **弹窗尺寸调整**: 将弹窗宽度调整为1200px，高度自适应，确保内容完整显示
  - **客户信息卡片美化**: 
    - 增加卡片阴影和圆角效果
    - 优化信息展示布局，使用8列网格展示更多客户信息
    - 添加悬停效果和渐变动画
    - 使用不同颜色区分数据类型（销售额、毛利、风险等）
  - **选项卡界面优化**:
    - 使用卡片式选项卡设计
    - 添加图标和emoji增强视觉效果
    - 优化选项卡切换动画
  - **表单界面美化**:
    - 增大表单控件尺寸，提升操作体验
    - 为下拉选项添加图标标识
    - 优化表单布局和间距
    - 增强表单验证和交互反馈
  - **按钮和交互优化**:
    - 使用渐变色按钮设计
    - 添加悬停和点击动画效果
    - 优化按钮图标和文字搭配
  - **查询区域美化**:
    - 使用渐变背景色
    - 优化查询表单布局
  - **表格样式优化**:
    - 增加表格阴影和圆角
    - 优化表头样式和悬停效果
    - 美化标签和状态显示
  - **响应式设计**: 完善不同屏幕尺寸下的显示效果
  - **动画效果**: 添加淡入动画和滚动条美化

### 用户体验优化
- **客户跟进记录操作优化**: 基于用户反馈进行的体验改进
  - **按钮位置优化**: 将保存和重置按钮移至表单顶部，使用粘性定位
    - 避免用户每次填写完表单都要滚动到底部点击保存
    - 按钮始终可见，提升操作便利性
    - 使用粘性定位和阴影效果，增强视觉层次
  - **查询区域背景优化**: 将查询表单背景从渐变色改为白色
    - 移除不协调的渐变背景色
    - 使用简洁的白色背景，提升视觉一致性
    - 保持边框和圆角设计，维持美观度
  - **表单滚动优化**: 为表单内容区域添加独立滚动
    - 设置表单最大高度为60vh
    - 确保在内容较多时可以独立滚动
    - 保持按钮区域始终可见

- **跟进记录表单与布局优化**:
  - **添加记录页**: 恢复了紧凑的水平表单布局（标签右对齐），并重新启用了"跟进类型"和"下次跟进时间"的智能默认值，提升了数据录入效率。
  - **查看记录页**: 保持原始的、非吸顶的简洁表格布局。
  - **整体**: 实现了模态框主标题的固定，并确保页面只有一个主滚动条，优化了整体滚动体验。

- **最终交互优化**:
  - **固定高度与内部滚动**: 模态框拥有固定高度。当跟进记录过多时，仅表格区域出现滚动条，确保客户信息、标签页、筛选条件始终可见。
  - **布局一致性**: 通过Flexbox布局，确保"添加"与"查看"两个标签页的内部容器大小一致，解决了因数据量不同导致页面大小变化的问题。

## 2025-01-15 - 生产环境标题配置修复

### 🎯 问题描述
- 生产环境部署后网站标题显示为 "SmartAdmin V3.X"
- 用户期望显示 "枫格后台管理系统"

### ✅ 解决方案
- **配置修改**: 修改 `.env.production` 文件中的 `VITE_APP_TITLE` 配置
- **构建更新**: 重新执行 `npm run build:prod` 生成新的构建文件

### 📋 修改详情

#### 环境配置文件更新
**文件**: `smart-admin-web-javascript/.env.production`

```diff
NODE_ENV=production
- VITE_APP_TITLE='SmartAdmin V3.X'
+ VITE_APP_TITLE='枫格后台管理系统'
VITE_APP_API_URL='/api'
```

### 🚀 部署说明
- 构建文件已生成至 `dist` 目录
- 需上传至腾讯云服务器替换现有文件
- 清除浏览器缓存后标题将正确显示

### 🔧 技术要点
- Vite 环境变量在构建时被替换到应用中
- `index.html` 中 `%VITE_APP_TITLE%` 占位符被正确处理
- 生产环境构建大小: 约 3.5MB (压缩后) 

## 2025-01-15 - 文件上传路径配置优化

### 🎯 问题发现
- 用户将上传的图片文件存储在 `/opt/smartadmin/frontend/uploads` 目录
- 后端配置的上传路径为 `/home/<USER>/upload/`
- 路径不匹配导致文件访问问题

### 💡 解决方案
提供两种配置方案：

#### 方案一：使用前端目录存储（推荐）
- **优势**: 配置简单，Nginx直接提供静态文件服务
- **配置**: 修改后端环境变量指向前端uploads目录
- **路径**: `/opt/smartadmin/frontend/uploads/`

#### 方案二：使用标准存储目录
- **优势**: 更规范的文件存储方式
- **配置**: 移动文件到标准目录并配置Docker挂载
- **路径**: `/home/<USER>/upload/`

### 🔧 关键配置点
1. **后端环境变量**: `SA_BASE_FILE_STORAGE_LOCAL_UPLOAD_PATH` 和 `SA_BASE_FILE_STORAGE_LOCAL_URL_PREFIX`
2. **Docker挂载**: 确保容器可以访问文件存储目录
3. **Nginx配置**: 正确配置静态文件访问路径
4. **权限设置**: 确保容器有读写权限

### 📋 技术要点
- Docker容器内外路径映射的重要性
- 环境变量在Spring Boot中的配置覆盖机制
- Nginx静态文件服务与反向代理的配合

### ✅ 最终解决方案
采用**方案一：使用前端目录存储**

#### 后端配置修改
1. **修改生产环境配置文件**: `sa-base/src/main/resources/prod/sa-base.yaml`
   ```yaml
   file:
     storage:
       local:
         upload-path: /usr/share/nginx/html/uploads/   # 使用前端目录
         url-prefix: /uploads/
   ```

2. **修改docker-compose.yml**: 添加环境变量和volume挂载
   ```yaml
   app:
     environment:
       - FILE_STORAGE_LOCAL_UPLOAD_PATH=/usr/share/nginx/html/uploads/
       - FILE_STORAGE_LOCAL_URL_PREFIX=/uploads/
     volumes:
       - ./frontend:/usr/share/nginx/html
   ```

#### 部署步骤
1. 创建uploads目录并设置权限
2. 更新docker-compose.yml配置
3. 重新构建并部署容器
4. 验证文件上传和访问功能

### 🎯 技术优势
- **配置简化**: 无需额外的nginx location配置
- **路径统一**: 后端和nginx共享同一文件存储目录
- **自动服务**: nginx自动提供静态文件服务
- **环境变量**: 通过环境变量灵活覆盖配置文件设置

### 🔍 问题排查记录

#### 部署验证发现的问题
1. **容器启动正常**: 所有容器都成功启动并运行
2. **Volume挂载缺失**: 后端容器内缺少 `/usr/share/nginx/html/uploads/` 目录
3. **配置未生效**: docker-compose.yml 中缺少必要的 volume 挂载和环境变量

#### 根本原因
- docker-compose.yml 配置文件没有正确更新
- 缺少 `./frontend:/usr/share/nginx/html` volume 挂载
- 缺少文件上传相关的环境变量配置

#### 解决方案
1. 更新 docker-compose.yml 添加 volume 挂载
2. 添加环境变量覆盖文件上传配置
3. 重新部署容器服务
4. 验证目录挂载和环境变量生效

### 新增功能
- ✨ **客户订单明细功能完整实现**
  - 实现客户跟进记录页面中的订单明细标签页真实数据展示
  - 连接lirun数据库的`订单明细`表，获取真实订单数据
  - 支持根据客户唯一编码查询该客户的所有订单明细
  - 支持按付款时间范围筛选订单数据
  - 显示原始单号、商家编码、数量、已付金额、付款时间等关键信息
  - 集成完整的分页查询功能

### 技术架构优化
- 🔧 **简化的数据源访问方案**
  - 创建LirunJdbcService直接使用JDBC连接lirun数据库
  - 避免复杂的多数据源配置，保持系统稳定性
  - 使用原生JDBC实现高效的数据查询
  - 独立的连接管理，不影响主系统数据源

### 后端API开发
- 🚀 **订单明细API完整实现**
  - 创建LirunJdbcService直接数据库访问服务
  - 实现CustomerOrderVO展示层对象
  - 开发CustomerOrderQueryForm查询表单类
  - 创建CustomerOrderService业务逻辑层
  - 开发CustomerOrderController控制器层
  - 使用原生JDBC实现高性能数据查询

### 前端功能增强
- 🎨 **客户跟进记录界面优化**
  - 移除订单明细的模拟数据，接入真实API
  - 优化错误处理和loading状态
  - 完善数据格式化和显示逻辑
  - 改进用户交互体验和反馈机制

### 数据库集成
- 📊 **lirun数据库无缝集成**
  - 数据库连接信息：**************:3306/lirun
  - 支持订单明细表的完整字段映射
  - 实现中文字段名到英文属性的转换
  - 优化查询性能和数据分页

### 核心功能特性
- **订单数据展示**：原始单号、商家编码、数量、已付金额、付款时间
- **客户关联查询**：根据客户唯一编码自动加载该客户的订单历史
- **时间范围筛选**：支持按付款时间范围查询订单
- **分页浏览**：完整的分页功能，支持页面大小调整
- **实时数据**：直接从生产数据库获取最新订单信息
- **错误处理**：完善的异常处理和用户提示机制