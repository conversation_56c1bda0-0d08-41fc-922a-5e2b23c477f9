<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.customer.dao.CustomerLinkDao">

    <select id="queryAnalysis" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkAnalysisVO">
        WITH
        -- CTE 1: 付款日期范围内的基准客户群体（用于计算付款人数）
        BaseCustomersForAnalysis AS (
            SELECT DISTINCT 客户唯一编码
            FROM lirun.订单明细
            <where>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND 平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND 付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND 付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
                AND 订单状态 != '线下退款'
                AND NOT (订单来源 = '手工创建' AND 订单状态 = '已取消')
                AND 标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (客服标旗 IS NULL OR 客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
            </where>
        ),

        -- CTE 2: 回购日期范围内的订单数据（仅限基准客户群体）
        RepurchaseOrdersForAnalysis AS (
            SELECT
                om.平台货品ID,
                om.客户唯一编码,
                om.付款时间,
                om.已付 AS 付款金额
            FROM lirun.订单明细 om
            INNER JOIN BaseCustomersForAnalysis bc ON om.客户唯一编码 = bc.客户唯一编码
            <where>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 回购日期范围筛选：如果设置了回购日期，使用回购日期；否则使用付款日期 -->
                <choose>
                    <when test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != '' and queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                        AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                        AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                    </when>
                    <otherwise>
                        <if test="queryForm.startDate != null and queryForm.startDate != ''">
                            AND om.付款时间 &gt;= #{queryForm.startDate}
                        </if>
                        <if test="queryForm.endDate != null and queryForm.endDate != ''">
                            AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                        </if>
                    </otherwise>
                </choose>
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
            </where>
        ),

        -- CTE 3: 客户类型筛选（如果需要）
        FilteredBaseCustomersForAnalysis AS (
            SELECT bc.客户唯一编码
            FROM BaseCustomersForAnalysis bc
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                WHERE CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = bc.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END = #{queryForm.customerType}
            </if>
        ),

        -- CTE 4: 计算回购期内每个客户的购买统计信息
        CustomerRepurchaseStats AS (
            SELECT
                ro.平台货品ID,
                ro.客户唯一编码,
                COUNT(DISTINCT DATE(ro.付款时间)) as 回购期购买天数,
                SUM(ro.付款金额) as 回购期购买金额,
                MIN(DATE(ro.付款时间)) as 回购期首次日期,
                MAX(DATE(ro.付款时间)) as 回购期最后日期
            FROM RepurchaseOrdersForAnalysis ro
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                INNER JOIN FilteredBaseCustomersForAnalysis fbc ON ro.客户唯一编码 = fbc.客户唯一编码
            </if>
            GROUP BY ro.平台货品ID, ro.客户唯一编码
        ),

        -- CTE 5: 为所有基准客户补充回购数据
        AllCustomersAnalysis AS (
            SELECT
                <choose>
                    <when test="queryForm.customerType != null and queryForm.customerType != ''">
                        fbc.客户唯一编码,
                    </when>
                    <otherwise>
                        bc.客户唯一编码,
                    </otherwise>
                </choose>
                COALESCE(crs.回购期购买天数, 0) as 回购期购买天数,
                COALESCE(crs.回购期购买金额, 0) as 回购期购买金额,
                crs.回购期首次日期,
                crs.回购期最后日期,
                -- 复购定义：回购期内购买天数>=2才算复购
                CASE
                    WHEN COALESCE(crs.回购期购买天数, 0) >= 2 THEN 1
                    ELSE 0
                END as 是否复购客户,
                -- 复购周期计算
                CASE
                    WHEN COALESCE(crs.回购期购买天数, 0) >= 2 THEN DATEDIFF(crs.回购期最后日期, crs.回购期首次日期)
                    ELSE NULL
                END as 复购周期天数
            FROM
            <choose>
                <when test="queryForm.customerType != null and queryForm.customerType != ''">
                    FilteredBaseCustomersForAnalysis fbc
                    LEFT JOIN CustomerRepurchaseStats crs ON fbc.客户唯一编码 = crs.客户唯一编码
                </when>
                <otherwise>
                    BaseCustomersForAnalysis bc
                    LEFT JOIN CustomerRepurchaseStats crs ON bc.客户唯一编码 = crs.客户唯一编码
                </otherwise>
            </choose>
        ),

        -- CTE 6: 提取复购客户的去重购买日期
        DistinctRepurchaseDates AS (
            SELECT DISTINCT
                ro.客户唯一编码,
                ro.平台货品ID,
                DATE(ro.付款时间) as purchase_date
            FROM RepurchaseOrdersForAnalysis ro
            WHERE ro.客户唯一编码 IN (SELECT 客户唯一编码 FROM AllCustomersAnalysis WHERE 是否复购客户 = 1)
        ),

        -- CTE 7: 计算复购客户的购买时间间隔
        RepurchaseIntervals AS (
            SELECT
                a.平台货品ID,
                a.客户唯一编码,
                DATEDIFF(next_purchase_date, purchase_date) as interval_days
            FROM (
                SELECT
                    客户唯一编码,
                    平台货品ID,
                    purchase_date,
                    LEAD(purchase_date, 1) OVER (PARTITION BY 客户唯一编码, 平台货品ID ORDER BY purchase_date) as next_purchase_date
                FROM DistinctRepurchaseDates
            ) a
            WHERE a.next_purchase_date IS NOT NULL
        ),

        -- CTE 8: 计算每个客户的间隔统计指标
        IntervalStatsNew AS (
            SELECT
                平台货品ID,
                客户唯一编码,
                MIN(interval_days) as 客户最小间隔,
                MAX(interval_days) as 客户最大间隔,
                AVG(interval_days) as 客户平均间隔
            FROM RepurchaseIntervals
            GROUP BY 平台货品ID, 客户唯一编码
        )

        -- 【最终聚合结果】
        SELECT
            COUNT(a.客户唯一编码) as paymentUserCount,
            SUM(a.是否复购客户) as repurchaseUserCount,
            ROUND(SUM(a.是否复购客户) * 100.0 / COUNT(a.客户唯一编码), 2) as repurchaseRate,
            ROUND(AVG(a.复购周期天数), 2) as avgRepurchaseCycle,
            MIN(i.客户最小间隔) as minRepurchaseInterval,
            MAX(i.客户最大间隔) as maxRepurchaseInterval,
            ROUND(AVG(i.客户平均间隔), 2) as avgRepurchaseInterval
        FROM AllCustomersAnalysis a
        LEFT JOIN IntervalStatsNew i ON a.客户唯一编码 = i.客户唯一编码
    </select>



    <select id="queryDetail" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkDetailVO">
        -- 链接复购率次数分布分析（增强版：支持回购日期筛选）
        -- 新逻辑：付款日期范围内的客户群体，在回购日期范围内的复购行为分析
        WITH
        -- CTE 1: 付款日期范围内的基准客户群体
        BaseCustomers AS (
            SELECT DISTINCT om.客户唯一编码
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND om.付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
            </where>
        ),

        -- CTE 2: 回购日期范围内的订单数据（仅限基准客户群体）
        RepurchaseOrders AS (
            SELECT om.客户唯一编码, om.数量, om.已付, DATE(om.付款时间) as 付款日期
            FROM lirun.订单明细 om
            INNER JOIN BaseCustomers bc ON om.客户唯一编码 = bc.客户唯一编码
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 回购日期范围筛选：如果设置了回购日期，使用回购日期；否则使用付款日期 -->
                <choose>
                    <when test="queryForm.repurchaseDateBegin != null and queryForm.repurchaseDateBegin != '' and queryForm.repurchaseDateEnd != null and queryForm.repurchaseDateEnd != ''">
                        AND om.付款时间 &gt;= #{queryForm.repurchaseDateBegin}
                        AND om.付款时间 &lt; DATE_ADD(#{queryForm.repurchaseDateEnd}, INTERVAL 1 DAY)
                    </when>
                    <otherwise>
                        <if test="queryForm.startDate != null and queryForm.startDate != ''">
                            AND om.付款时间 &gt;= #{queryForm.startDate}
                        </if>
                        <if test="queryForm.endDate != null and queryForm.endDate != ''">
                            AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                        </if>
                    </otherwise>
                </choose>
            </where>
        ),

        -- CTE 3: 客户类型筛选（如果需要）
        FilteredBaseCustomers AS (
            SELECT bc.客户唯一编码
            FROM BaseCustomers bc
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                WHERE CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = bc.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END = #{queryForm.customerType}
            </if>
        ),
        -- CTE 4: 回购日期范围内按天聚合的购买数据（仅限筛选后的基准客户）
        DailyRepurchaseData AS (
            SELECT
                ro.客户唯一编码,
                ro.付款日期,
                SUM(ro.数量) as daily_quantity,
                SUM(ro.已付) as daily_paid
            FROM RepurchaseOrders ro
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                INNER JOIN FilteredBaseCustomers fbc ON ro.客户唯一编码 = fbc.客户唯一编码
            </if>
            GROUP BY ro.客户唯一编码, ro.付款日期
        ),

        -- CTE 5: 计算每个客户在回购期内的购买次数和总数据
        CustomerRepurchaseSummary AS (
            SELECT
                客户唯一编码,
                COUNT(DISTINCT 付款日期) as 复购次数,
                SUM(daily_quantity) as 总件数,
                SUM(daily_paid) as 总金额,
                MIN(付款日期) as 首次购买日期,
                MAX(付款日期) as 最后购买日期
            FROM DailyRepurchaseData
            GROUP BY 客户唯一编码
        ),

        -- CTE 6: 为所有基准客户补充复购数据（包括在回购期内无购买的客户）
        AllCustomersWithRepurchaseData AS (
            SELECT
                <choose>
                    <when test="queryForm.customerType != null and queryForm.customerType != ''">
                        fbc.客户唯一编码,
                    </when>
                    <otherwise>
                        bc.客户唯一编码,
                    </otherwise>
                </choose>
                COALESCE(crs.复购次数, 0) as 复购次数,
                COALESCE(crs.总件数, 0) as 总件数,
                COALESCE(crs.总金额, 0) as 总金额,
                crs.首次购买日期,
                crs.最后购买日期
            FROM
            <choose>
                <when test="queryForm.customerType != null and queryForm.customerType != ''">
                    FilteredBaseCustomers fbc
                    LEFT JOIN CustomerRepurchaseSummary crs ON fbc.客户唯一编码 = crs.客户唯一编码
                </when>
                <otherwise>
                    BaseCustomers bc
                    LEFT JOIN CustomerRepurchaseSummary crs ON bc.客户唯一编码 = crs.客户唯一编码
                </otherwise>
            </choose>
        )
        -- 最终查询: 基于新逻辑的复购明细分析
        -- 按复购次数分组统计
        SELECT
            CASE
                WHEN 复购次数 = 0 THEN '第0次'
                ELSE CONCAT('第', 复购次数, '次')
            END as repurchaseTimes,
            COUNT(*) as repurchaseCustomers,
            SUM(总件数) as repurchaseQuantity,
            ROUND(SUM(总金额), 2) as repurchaseAmount,
            CASE
                WHEN 复购次数 >= 2 THEN
                    ROUND(AVG(DATEDIFF(最后购买日期, 首次购买日期)), 2)
                ELSE NULL
            END as avgRepurchaseCycleDays
        FROM AllCustomersWithRepurchaseData
        GROUP BY 复购次数

        UNION ALL

        -- 合计行
        SELECT
            '合计' as repurchaseTimes,
            COUNT(*) as repurchaseCustomers,
            SUM(CASE WHEN 复购次数 > 0 THEN 总件数 ELSE 0 END) as repurchaseQuantity,
            ROUND(SUM(CASE WHEN 复购次数 > 0 THEN 总金额 ELSE 0 END), 2) as repurchaseAmount,
            ROUND(AVG(CASE WHEN 复购次数 >= 2 THEN DATEDIFF(最后购买日期, 首次购买日期) ELSE NULL END), 2) as avgRepurchaseCycleDays
        FROM AllCustomersWithRepurchaseData

        UNION ALL

        -- 人均行
        SELECT
            '人均' as repurchaseTimes,
            NULL as repurchaseCustomers,
            ROUND(SUM(CASE WHEN 复购次数 > 0 THEN 总件数 ELSE 0 END) / COUNT(*), 2) as repurchaseQuantity,
            ROUND(SUM(CASE WHEN 复购次数 > 0 THEN 总金额 ELSE 0 END) / COUNT(*), 2) as repurchaseAmount,
            ROUND(AVG(CASE WHEN 复购次数 >= 2 THEN DATEDIFF(最后购买日期, 首次购买日期) ELSE NULL END), 2) as avgRepurchaseCycleDays
        FROM AllCustomersWithRepurchaseData

        ORDER BY
            CASE
                WHEN repurchaseTimes LIKE '第%次' THEN CAST(SUBSTRING(repurchaseTimes, 2, CHAR_LENGTH(repurchaseTimes) - 2) AS UNSIGNED)
                WHEN repurchaseTimes = '人均' THEN 999
                WHEN repurchaseTimes = '合计' THEN 1000
                ELSE 0
            END
    </select>

    <!-- 下载复购明细的订单明细数据 -->
    <select id="downloadOrderDetail" resultType="net.lab1024.sa.admin.module.business.customer.domain.vo.CustomerLinkOrderDetailVO">
        -- 下载明细查询（基于用户提供的SQL逻辑）
        -- 此查询用于数据核对，会列出所有底层订单
        WITH
        -- CTE 1: 筛选基础订单，与主报告逻辑一致
        FilteredOrders AS (
            SELECT om.客户唯一编码, om.数量, om.已付, DATE(om.付款时间) as 付款日期
            FROM lirun.订单明细 om
            <where>
                om.平台货品ID IS NOT NULL AND om.平台货品ID != ''
                AND om.客户唯一编码 IS NOT NULL AND om.客户唯一编码 != ''
                AND om.付款时间 IS NOT NULL
                AND om.订单状态 != '线下退款'
                AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
                AND om.标记名称 != '贴贴贴贴贴'
                <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                    AND (om.客服标旗 IS NULL OR om.客服标旗 NOT IN
                    <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                        #{flag}
                    </foreach>)
                </if>
                <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                    AND om.平台货品ID IN
                    <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryForm.startDate != null and queryForm.startDate != ''">
                    AND om.付款时间 &gt;= #{queryForm.startDate}
                </if>
                <if test="queryForm.endDate != null and queryForm.endDate != ''">
                    AND om.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
                </if>
            </where>
        ),

        -- CTE 1.5: 基于lirun数据库实时计算客户分类
        FilteredOrdersWithCustomerType AS (
            SELECT
                fo.客户唯一编码,
                fo.数量,
                fo.已付,
                fo.付款日期,
                CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END as 客户类型
            FROM FilteredOrders fo
            <if test="queryForm.customerType != null and queryForm.customerType != ''">
                WHERE CASE
                    WHEN EXISTS (
                        SELECT 1 FROM lirun.订单明细 hist
                        WHERE hist.客户唯一编码 = fo.客户唯一编码
                            AND hist.付款时间 &lt; #{queryForm.startDate}
                            AND hist.订单状态 != '线下退款'
                            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
                            AND hist.标记名称 != '贴贴贴贴贴'
                    ) THEN '老客'
                    ELSE '新客'
                END = #{queryForm.customerType}
            </if>
        ),
        -- CTE 2: 按天聚合，计算每日的购买事件
        DailyAggregatedPurchases AS (
            SELECT 客户唯一编码, 付款日期
            FROM FilteredOrdersWithCustomerType GROUP BY 客户唯一编码, 付款日期
        ),
        -- CTE 3: 计算每个客户的购买次序和总购买次数
        CustomerPurchaseStats AS (
            SELECT
                客户唯一编码,
                付款日期,
                ROW_NUMBER() OVER (PARTITION BY 客户唯一编码 ORDER BY 付款日期) as purchase_order,
                COUNT(*) OVER (PARTITION BY 客户唯一编码) as total_purchases
            FROM DailyAggregatedPurchases
        )
        -- 最终查询：将分析维度关联回原始的订单明细表
        SELECT
            -- 分析维度
            CASE
                WHEN cps.purchase_order = 1 THEN '首次购买'
                ELSE '复购'
            END as repurchaseTimes,
            cps.total_purchases as purchaseOrder,
            cps.purchase_order as repurchaseCycleDays,
            -- 原始订单明细
            od.客户唯一编码 as customerUniqueCode,
            od.原始单号 as originalOrderNo,
            od.平台货品ID as platformGoodsId,
            od.商家编码 as merchantCode,
            od.数量 as quantity,
            od.已付 as paidAmount,
            od.付款时间 as paymentTime,
            od.店铺名称 as shopName
        FROM
            lirun.订单明细 od
        JOIN
            CustomerPurchaseStats cps ON od.客户唯一编码 = cps.客户唯一编码 AND DATE(od.付款时间) = cps.付款日期
        <where>
            -- 再次应用核心过滤条件，确保关联的准确性
            od.订单状态 != '线下退款'
            AND NOT (od.订单来源 = '手工创建' AND od.订单状态 = '已取消')
            AND od.标记名称 != '贴贴贴贴贴'
            <if test="queryForm.platformGoodsIds != null and queryForm.platformGoodsIds.size() > 0">
                AND od.平台货品ID IN
                <foreach collection="queryForm.platformGoodsIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryForm.startDate != null and queryForm.startDate != ''">
                AND od.付款时间 &gt;= #{queryForm.startDate}
            </if>
            <if test="queryForm.endDate != null and queryForm.endDate != ''">
                AND od.付款时间 &lt; DATE_ADD(#{queryForm.endDate}, INTERVAL 1 DAY)
            </if>
            <if test="queryForm.excludeFlag != null and queryForm.excludeFlag != ''">
                AND (od.客服标旗 IS NULL OR od.客服标旗 NOT IN
                <foreach collection="queryForm.excludeFlag.split(',')" item="flag" open="(" separator="," close=")">
                    #{flag}
                </foreach>)
            </if>
        </where>
        ORDER BY
            od.客户唯一编码,
            od.付款时间
    </select>

</mapper>