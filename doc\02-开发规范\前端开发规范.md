# SmartAdmin 标准页面布局规范

## 概述

本文档定义了 SmartAdmin 系统中标准列表页面的布局规范，确保所有页面保持一致的视觉风格和用户体验。

## 标准布局结构

### 1. 整体布局层次

```
页面容器
├── 查询表单区域 (smart-query-form)
└── 卡片容器 (a-card)
    ├── 表格操作行 (TableOperator)
    ├── 数据表格 (a-table)
    └── 分页组件 (smart-query-table-page)
```

### 2. 查询表单区域

**位置**: 卡片外部，页面顶部
**样式类**: `smart-query-form`
**特点**:
- 背景色为白色
- 具有标准的内边距 (`padding: 5px 10px`)
- 底部间距 (`margin-bottom: 10px`)
- 使用 `smart-query-form-row` 和 `smart-query-form-item` 进行布局

**标准代码结构**:
```vue
<a-form class="smart-query-form" ref="queryFormRef">
    <a-row class="smart-query-form-row">
        <a-form-item label="字段名" class="smart-query-form-item">
            <a-input v-model:value="queryForm.field" placeholder="请输入" />
        </a-form-item>
        <a-form-item class="smart-query-form-item">
            <a-button type="primary" @click="onSearch">查询</a-button>
            <a-button @click="resetQuery" class="smart-margin-left10">重置</a-button>
        </a-form-item>
    </a-row>
</a-form>
```

### 3. 卡片容器

**样式属性**: `size="small" :bordered="false" :hoverable="true"`
**特点**:
- 小尺寸卡片，无边框，支持悬停效果
- 不添加额外的 margin-bottom（系统会自动处理间距）

### 4. 表格操作行

**位置**: 卡片内部顶部
**布局方式**: 右对齐 (`justify="end"`)
**组件**: 使用 `TableOperator` 组件
**样式**: 添加 `smart-margin-bottom5` 类

**标准代码结构**:
```vue
<a-row justify="end" ref="tableOperatorRef">
    <TableOperator 
        class="smart-margin-bottom5" 
        v-model="columns" 
        :tableId="TABLE_ID_CONST.XXX" 
        :refresh="queryData" 
    />
</a-row>
```

### 5. 数据表格

**配置**:
- `size="small"` - 小尺寸表格
- `:pagination="false"` - 禁用表格自带分页
- `:scroll="{ y: scrollY }"` - 启用垂直滚动
- `bordered` - 显示边框

### 6. 分页组件

**位置**: 卡片内部底部
**样式类**: `smart-query-table-page`
**特点**:
- 右对齐显示
- 顶部间距 (`margin-top: 10px`)
- 支持页面大小选择、快速跳转等功能

**标准代码结构**:
```vue
<div class="smart-query-table-page">
    <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
    />
</div>
```

## 表格高度自适应

### 配置要求

1. **引入工具函数**:
```javascript
import { calcTableHeight } from '/@/lib/table-auto-height';
```

2. **定义响应式变量**:
```javascript
const scrollY = ref(100);
const tableOperatorRef = ref();
const queryFormRef = ref();
```

3. **高度计算函数**:
```javascript
function autoCalcTableHeight() {
    calcTableHeight(scrollY, [tableOperatorRef, queryFormRef], 10);
}
```

4. **生命周期处理**:
```javascript
window.addEventListener('resize', autoCalcTableHeight);

onMounted(() => {
    queryData();
    autoCalcTableHeight();
});

onUnmounted(() => {
    window.removeEventListener('resize', autoCalcTableHeight);
});
```

## 参考页面

以下页面严格遵循此布局规范：
- 登录登出记录页面 (`/views/support/login-log/login-log-list.vue`)
- 任务发布页面 (`/views/business/task/task-list.vue`)
- 客户视角页面 (`/views/business/customer/customer-view.vue`)

## 注意事项

1. **不要使用固定定位的分页**: 分页组件应始终在卡片内部，不要使用 `position: fixed`
2. **保持样式类的一致性**: 严格使用系统定义的样式类，如 `smart-query-form`、`smart-query-table-page` 等
3. **表格操作行的简洁性**: 优先使用 `TableOperator` 组件，避免复杂的自定义操作按钮布局
4. **响应式设计**: 确保页面在不同屏幕尺寸下都能正常显示
5. **间距统一**: 使用系统预定义的间距类，如 `smart-margin-left10`、`smart-margin-bottom5` 等

## 完整模板

```vue
<template>
    <!-- 查询表单 -->
    <a-form class="smart-query-form" ref="queryFormRef">
        <a-row class="smart-query-form-row">
            <!-- 查询字段 -->
            <a-form-item label="字段名" class="smart-query-form-item">
                <a-input v-model:value="queryForm.field" placeholder="请输入" />
            </a-form-item>
            <!-- 操作按钮 -->
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="onSearch">查询</a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">重置</a-button>
            </a-form-item>
        </a-row>
    </a-form>

    <!-- 主要内容卡片 -->
    <a-card size="small" :bordered="false" :hoverable="true">
        <!-- 表格操作行 -->
        <a-row justify="end" ref="tableOperatorRef">
            <TableOperator 
                class="smart-margin-bottom5" 
                v-model="columns" 
                :tableId="TABLE_ID_CONST.XXX" 
                :refresh="queryData" 
            />
        </a-row>

        <!-- 数据表格 -->
        <a-table
            size="small"
            :dataSource="tableData"
            :columns="columns"
            rowKey="id"
            bordered
            :loading="tableLoading"
            :pagination="false"
            :scroll="{ y: scrollY }"
        >
            <!-- 表格内容模板 -->
        </a-table>

        <!-- 分页组件 -->
        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize"
                v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize"
                :total="total"
                @change="queryData"
                @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`"
            />
        </div>
    </a-card>
</template>

<script setup>
import { reactive, ref, onMounted, onUnmounted } from 'vue';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { calcTableHeight } from '/@/lib/table-auto-height';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';

// 表格高度自适应
const scrollY = ref(100);
const tableOperatorRef = ref();
const queryFormRef = ref();

function autoCalcTableHeight() {
    calcTableHeight(scrollY, [tableOperatorRef, queryFormRef], 10);
}

// 生命周期
onMounted(() => {
    queryData();
    autoCalcTableHeight();
});

onUnmounted(() => {
    window.removeEventListener('resize', autoCalcTableHeight);
});

window.addEventListener('resize', autoCalcTableHeight);
</script>
```

## 日期组件开发规范

### 1. 基础日期选择器

**标准配置**:
```vue
<a-form-item label="日期字段" class="smart-query-form-item">
    <a-range-picker
        v-model:value="dateRange"
        @change="onDateChange"
        :presets="defaultTimeRanges"
        style="width: 240px"
        placeholder="请选择日期范围"
    />
</a-form-item>
```

**关键要求**:
- 统一使用 240px 宽度
- 必须提供 placeholder 提示
- 使用 `defaultTimeRanges` 预设范围
- 遵循 `smart-query-form-item` 样式规范

### 2. 约束型日期选择器

**适用场景**: 当一个日期选择器需要基于另一个日期选择器的值进行约束时

**实现规范**:
```vue
<template>
    <!-- 主日期选择器 -->
    <a-form-item label="付款日期" class="smart-query-form-item">
        <a-range-picker
            v-model:value="paymentDateRange"
            @change="onChangePaymentDate"
            :presets="defaultTimeRanges"
            style="width: 240px"
            placeholder="请选择付款日期范围"
        />
    </a-form-item>

    <!-- 约束型日期选择器 -->
    <a-form-item label="回购日期" class="smart-query-form-item">
        <a-range-picker
            ref="constrainedDatePicker"
            v-model:value="constrainedDateRange"
            @change="onChangeConstrainedDate"
            :presets="defaultTimeRanges"
            :disabledDate="disabledConstrainedDate"
            style="width: 240px"
            placeholder="请选择回购日期范围"
        />
    </a-form-item>
</template>

<script setup>
// 约束逻辑函数
function disabledConstrainedDate(current) {
    if (!paymentDateRange.value || paymentDateRange.value.length !== 2) {
        return true; // 未选择主日期时，禁用所有约束日期
    }

    const [startDate, endDate] = paymentDateRange.value;

    // 约束日期必须在主日期范围内
    return current && (
        current.isBefore(startDate, 'day') ||
        current.isAfter(endDate, 'day')
    );
}

// 主日期变更处理
function onChangePaymentDate(dates) {
    if (dates && dates.length === 2) {
        // 更新主日期
        queryForm.paymentDateBegin = dates[0];
        queryForm.paymentDateEnd = dates[1];

        // 自动同步约束日期
        constrainedDateRange.value = [dates[0], dates[1]];
        queryForm.constrainedDateBegin = dates[0];
        queryForm.constrainedDateEnd = dates[1];
    } else {
        // 清空所有相关日期
        clearAllDates();
    }
}
</script>
```

### 3. 禁用日期点击检测

**适用场景**: 需要为用户点击禁用日期提供具体提示信息时

**实现规范**:
```javascript
// 事件监听器管理
let clickEventListener = null;

// 添加禁用日期点击监听器
function addDisabledDateClickListener() {
    removeDisabledDateClickListener();

    nextTick(() => {
        if (datePickerRef.value && datePickerRef.value.$el) {
            const pickerElement = datePickerRef.value.$el;

            clickEventListener = function(event) {
                const clickedCell = event.target.closest('.ant-picker-cell');
                if (!clickedCell) return;

                if (clickedCell.classList.contains('ant-picker-cell-disabled')) {
                    const clickedDate = parseDateFromCell(clickedCell);
                    if (clickedDate) {
                        showDisabledDateTip(clickedDate);
                    }
                }
            };

            // 使用捕获阶段监听
            pickerElement.addEventListener('click', clickEventListener, true);
        }
    });
}

// 移除监听器
function removeDisabledDateClickListener() {
    if (clickEventListener && datePickerRef.value && datePickerRef.value.$el) {
        datePickerRef.value.$el.removeEventListener('click', clickEventListener, true);
        clickEventListener = null;
    }
}

// 组件生命周期管理
onMounted(() => {
    if (needsClickDetection) {
        addDisabledDateClickListener();
    }
});

onUnmounted(() => {
    removeDisabledDateClickListener();
});
```

### 4. 日期解析和提示

**日期解析函数**:
```javascript
function parseDateFromCell(cell) {
    try {
        // 方法1: 从title属性解析
        const title = cell.getAttribute('title');
        if (title) {
            const parsed = dayjs(title);
            if (parsed.isValid()) return parsed;
        }

        // 方法2: 从DOM结构解析
        const cellText = cell.textContent.trim();
        if (cellText && /^\d{1,2}$/.test(cellText)) {
            // 获取年月信息并构造完整日期
            // ... 具体实现逻辑
        }

        return null;
    } catch (error) {
        console.warn('解析日期失败:', error);
        return null;
    }
}
```

**提示信息规范**:
```javascript
function showDisabledDateTip(clickedDate) {
    const [startDate, endDate] = mainDateRange.value;

    if (clickedDate.isBefore(startDate, 'day')) {
        message.warning(`日期不能早于开始日期（${startDate.format('YYYY-MM-DD')}）`);
    } else if (clickedDate.isAfter(endDate, 'day')) {
        message.warning(`日期不能晚于结束日期（${endDate.format('YYYY-MM-DD')}）`);
    }
}
```

### 5. 开发最佳实践

#### 5.1 数据绑定规范
```javascript
// 查询表单状态
const queryForm = reactive({
    mainDateBegin: null,
    mainDateEnd: null,
    constrainedDateBegin: null,
    constrainedDateEnd: null,
});

// 日期范围响应式变量
const mainDateRange = ref([]);
const constrainedDateRange = ref([]);
```

#### 5.2 事件处理规范
- **统一命名**: 使用 `onChange[FieldName]Date` 格式
- **参数验证**: 始终检查 dates 参数的有效性
- **状态同步**: 确保响应式变量与查询表单状态同步
- **错误处理**: 提供适当的错误处理和降级方案

#### 5.3 性能优化规范
- **事件委托**: 优先使用事件委托而非多个事件监听器
- **防抖处理**: 对频繁触发的事件进行防抖处理
- **内存管理**: 正确管理事件监听器的生命周期
- **条件监听**: 只在必要时添加事件监听器

#### 5.4 可访问性规范
- **键盘导航**: 确保日期选择器支持键盘导航
- **屏幕阅读器**: 提供适当的 aria 标签和描述
- **对比度**: 确保禁用状态的视觉对比度符合标准
- **焦点管理**: 正确管理焦点状态和顺序

### 6. 常见问题和解决方案

#### 6.1 日期同步问题
**问题**: 约束日期不能正确同步主日期的变更
**解决**: 确保在主日期变更时正确更新约束日期的值和范围

#### 6.2 事件监听器泄漏
**问题**: 组件销毁后事件监听器未正确清理
**解决**: 在 `onUnmounted` 钩子中移除所有事件监听器

#### 6.3 日期解析失败
**问题**: 无法正确解析被点击的禁用日期
**解决**: 提供多种解析方法，并添加适当的错误处理

#### 6.4 浏览器兼容性
**问题**: 某些浏览器不支持特定的日期操作
**解决**: 使用 dayjs 库进行日期操作，确保跨浏览器兼容性

---

**版本**: v1.1
**更新时间**: 2025-08-02
**适用范围**: SmartAdmin 系统所有列表页面和日期组件开发