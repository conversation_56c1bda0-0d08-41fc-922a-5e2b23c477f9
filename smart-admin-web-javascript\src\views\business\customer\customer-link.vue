<!--
  * 链接视角页面
  *
  * @Author:    汪波
  * @Date:      2025-01-15 16:00:00
  * @Copyright  1.0
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form" ref="queryFormRef">
        <a-row class="smart-query-form-row">
            <!-- 客户编码功能暂未实现，先注释掉 -->
            <!-- <a-form-item label="客户编码" class="smart-query-form-item">
                <a-input style="width: 200px" v-model:value="queryForm.customerUniqueCode" placeholder="客户编码" />
            </a-form-item> -->
            <a-form-item label="客户特征" class="smart-query-form-item">
                <a-select style="width: 150px" v-model:value="queryForm.customerType" placeholder="请选择客户特征">
                    <a-select-option value="">不限</a-select-option>
                    <a-select-option value="新客">新客</a-select-option>
                    <a-select-option value="老客">老客</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="付款日期" class="smart-query-form-item">
                <a-range-picker
                    v-model:value="paymentDateRange"
                    @change="onChangePaymentDate"
                    :presets="defaultTimeRanges"
                    style="width: 240px"
                />
            </a-form-item>
            <a-form-item label="回购日期" class="smart-query-form-item">
                <a-range-picker
                    ref="repurchaseDatePicker"
                    v-model:value="repurchaseDateRange"
                    @change="onChangeRepurchaseDate"
                    :presets="defaultTimeRanges"
                    :disabledDate="disabledRepurchaseDate"
                    style="width: 240px"
                    placeholder="请选择回购日期范围"
                />
            </a-form-item>
                            <a-form-item label="货品ID" class="smart-query-form-item">
                <a-input 
                    style="width: 300px; cursor: pointer" 
                    v-model:value="selectedProductText" 
                    placeholder="点击选择货品ID" 
                    readonly
                    @click="showProductSelectModal"
                >
                    <template #suffix>
                        <SelectOutlined />
                    </template>
                </a-input>
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="showStatistics" v-privilege="'customer:link:query'">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10" v-privilege="'customer:link:reset'">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row justify="space-between" ref="tableOperatorRef">
            <div class="smart-table-operate-block">
                <a-button type="primary" @click="showStatistics" v-privilege="'customer:link:statistics'" style="display: none;">
                    <template #icon>
                        <BarChartOutlined />
                    </template>
                    统计分析
                </a-button>
                <div class="exclude-flags-container">
                    <span class="exclude-label">排除旗帜:</span>
                    <a-checkbox-group v-model:value="excludeFlags" class="flag-checkbox-group">
                        <a-checkbox value="红" class="flag-checkbox">
                            <span class="flag-icon red-flag"></span>
                        </a-checkbox>
                        <a-checkbox value="蓝" class="flag-checkbox">
                            <span class="flag-icon blue-flag"></span>
                        </a-checkbox>
                        <a-checkbox value="绿" class="flag-checkbox">
                            <span class="flag-icon green-flag"></span>
                        </a-checkbox>
                        <a-checkbox value="黄" class="flag-checkbox">
                            <span class="flag-icon yellow-flag"></span>
                        </a-checkbox>
                        <a-checkbox value="紫" class="flag-checkbox">
                            <span class="flag-icon purple-flag"></span>
                        </a-checkbox>
                        <a-checkbox value="灰" class="flag-checkbox">
                            <span class="flag-icon gray-flag"></span>
                        </a-checkbox>
                    </a-checkbox-group>
                </div>
            </div>
            <div class="right-controls">
                <a-radio-group v-model:value="analysisType" class="analysis-type-group">
                    <a-radio value="repurchaseCustomers">复购人数</a-radio>
                    <a-radio value="repurchaseQuantity">复购件数</a-radio>
                    <a-radio value="repurchaseAmount">复购金额</a-radio>
                    <a-radio value="avgRepurchaseCycle">平均复购周期</a-radio>
                </a-radio-group>
                <TableOperator class="smart-margin-bottom5" v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.CUSTOMER_LINK" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 统计分析区域 begin ----------->
        <div v-if="showStatisticsPanel" class="statistics-panel">
            <a-card title="链接分析统计" class="statistics-card">
                <template #extra>
                    <a-button type="text" @click="hideStatistics">
                        <template #icon>
                            <CloseOutlined />
                        </template>
                        收起
                    </a-button>
                </template>



                <!-- 核心指标卡片 -->
                <a-row :gutter="16" class="metrics-row">
                    <a-col :span="6">
                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-title">平均复购周期</span>
                                <a-tooltip title="复购客户从首次购买到最后一次购买的平均天数，反映客户复购的时间间隔">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </div>
                            <div class="metric-value">{{ statisticsData?.avgRepurchaseCycle || '-' }}</div>
                            <div class="metric-unit">天</div>
                        </div>
                    </a-col>
                    <a-col :span="6">
                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-title">最小复购间隔</span>
                                <a-tooltip title="所有复购客户中，相邻两次购买间隔的最小天数">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </div>
                            <div class="metric-value">{{ statisticsData?.minRepurchaseInterval || '-' }}</div>
                            <div class="metric-unit">天</div>
                        </div>
                    </a-col>
                    <a-col :span="6">
                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-title">最大复购间隔</span>
                                <a-tooltip title="所有复购客户中，相邻两次购买间隔的最大天数">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </div>
                            <div class="metric-value">{{ statisticsData?.maxRepurchaseInterval || '-' }}</div>
                            <div class="metric-unit">天</div>
                        </div>
                    </a-col>
                    <a-col :span="6">
                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-title">平均复购间隔</span>
                                <a-tooltip title="所有复购客户相邻两次购买间隔的平均天数，反映整体复购频率">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </div>
                            <div class="metric-value">{{ statisticsData?.avgRepurchaseInterval || '-' }}</div>
                            <div class="metric-unit">天</div>
                        </div>
                    </a-col>
                </a-row>

                <!-- 复购率分析和复购次数分布 -->
                <a-row :gutter="16" class="charts-row">
                    <!-- 复购率分析 -->
                    <a-col :span="12">
                        <div class="chart-container">
                            <h4>
                                复购率分析
                                <a-tooltip title="复购人数 ÷ 付款人数 × 100%，反映客户重复购买的比例">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </h4>
                            <div class="repurchase-analysis">
                                <div class="circle-progress">
                                    <div class="progress-circle" :key="animationKey" :style="circleProgressStyle">
                                        <div class="progress-text">
                                            <div class="percentage">{{ statisticsData?.repurchaseRate || 0 }}%</div>
                                            <div class="label">复购率</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="stats-side">
                                    <div class="stat-row">
                                        <span class="stat-label">
                                            复购人数
                                            <a-tooltip title="在指定时间范围内，对同一平台货品ID有≥2天购买记录的客户数量（精准复购定义）">
                                                <QuestionCircleOutlined class="help-icon" />
                                            </a-tooltip>
                                        </span>
                                        <span class="stat-dot repurchase"></span>
                                        <span class="stat-value">{{ statisticsData?.repurchaseUserCount || 0 }}人</span>
                                    </div>
                                    <div class="stat-row">
                                        <span class="stat-label">
                                            付款人数
                                            <a-tooltip title="在指定时间范围内，对选定平台货品ID有付款记录的客户总数">
                                                <QuestionCircleOutlined class="help-icon" />
                                            </a-tooltip>
                                        </span>
                                        <span class="stat-dot single"></span>
                                        <span class="stat-value">{{ statisticsData?.paymentUserCount || 0 }}人</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a-col>
                    
                    <!-- 复购次数分布 -->
                    <a-col :span="12">
                        <div class="chart-container">
                            <h4>复购次数分布</h4>
                            <div ref="barChartRef" class="bar-chart"></div>
                        </div>
                    </a-col>
                </a-row>

                <!-- 复购明细表格 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h4>复购明细</h4>
                        <a-button type="link" size="small" @click="downloadDetail" v-privilege="'customer:link:download'">
                            <template #icon>
                                <DownloadOutlined />
                            </template>
                            下载明细
                        </a-button>
                    </div>
                    <a-table
                        :columns="detailColumns"
                        :dataSource="detailData"
                        :pagination="false"
                        size="small"
                        class="detail-table"
                        :customRow="(record) => {
                            return {
                                class: record.repurchaseTimes === '人均' || record.repurchaseTimes === '合计' ? 'summary-row' : ''
                            };
                        }"
                    >
                        <template #headerCell="{ column }">
                            <template v-if="column.key === 'repurchaseTimes'">
                                <span>复购次数</span>
                                <a-tooltip title="客户对同一平台货品ID的重复购买次数分组">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </template>
                            <template v-else-if="column.key === 'repurchaseCustomers'">
                                <span>复购人数</span>
                                <a-tooltip title="该复购次数分组下的客户数量">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </template>
                            <template v-else-if="column.key === 'repurchaseQuantity'">
                                <span>复购件数</span>
                                <a-tooltip title="该复购次数分组下的商品购买总件数">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </template>
                            <template v-else-if="column.key === 'repurchaseAmount'">
                                <span>复购金额</span>
                                <a-tooltip title="该复购次数分组下的购买总金额">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </template>
                            <template v-else-if="column.key === 'avgRepurchaseCycleDays'">
                                <span>平均复购周期天数</span>
                                <a-tooltip title="该复购次数分组下客户的平均复购间隔天数">
                                    <QuestionCircleOutlined class="help-icon" />
                                </a-tooltip>
                            </template>
                            <template v-else>
                                {{ column.title }}
                            </template>
                        </template>
                        <template #bodyCell="{ text, record, column }">
                            <span :class="{ 'summary-text': record.repurchaseTimes === '合计' || record.repurchaseTimes === '均值' }">
                                <template v-if="column.dataIndex === 'repurchaseCustomers' || column.dataIndex === 'repurchaseQuantity'">
                                    {{ formatNumber(text) }}
                                </template>
                                <template v-else>
                                    {{ text }}
                                </template>
                            </span>
                        </template>
                    </a-table>
                </div>
            </a-card>
        </div>
                 <!---------- 统计分析区域 end ----------->

        <!---------- 提示信息 begin ----------->
        <div v-if="!showStatisticsPanel" class="empty-state">
                            <a-empty description="请选择货品ID后点击统计分析查看数据">
                <template #image>
                    <BarChartOutlined style="font-size: 48px; color: #d9d9d9;" />
                </template>
            </a-empty>
        </div>
        <!---------- 提示信息 end ----------->
    </a-card>

    <!-- 商品选择弹窗 -->
    <ProductSelectModal 
        ref="productSelectModalRef" 
        @confirm="onProductSelected" 
    />

    <!-- 统计分析模态框 -->
    <CustomerLinkStatisticsModal ref="statisticsModalRef" />

</template>

<script setup>
    import { reactive, ref, onMounted, onUnmounted, computed, watch, h } from 'vue';
    import { message } from 'ant-design-vue';
    import { SearchOutlined, ReloadOutlined, SelectOutlined, BarChartOutlined, CloseOutlined, QuestionCircleOutlined, DownloadOutlined } from '@ant-design/icons-vue';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
    import { defaultTimeRanges } from '/@/lib/default-time-ranges';
    import ProductSelectModal from './components/product-select-modal.vue';
    import CustomerLinkStatisticsModal from './components/customer-link-statistics-modal.vue';
    import * as echarts from 'echarts';
    import { nextTick } from 'vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { customerLinkApi } from '/@/api/business/customer/customer-link-api';
    import _ from 'lodash';
    import dayjs from 'dayjs';

    // ---------------------------- 页面引用 ----------------------------
    const tableOperatorRef = ref();
    const queryFormRef = ref();
    const repurchaseDatePicker = ref();

    // ---------------------------- 查询数据表单和方法 ----------------------------
    const queryFormState = {
        customerUniqueCode: '',
        customerType: '', // 客户特征：不限、新客、老客
        paymentDateBegin: null, // 付款日期开始
        paymentDateEnd: null, // 付款日期结束
        repurchaseDateBegin: null, // 回购日期开始
        repurchaseDateEnd: null, // 回购日期结束
        selectedProductIds: [], // 选中的货品ID列表
        pageNum: 1,
        pageSize: 50,
        searchCount: true,
        sortCode: '',
        sortType: '',
    };

    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 付款日期范围
    const paymentDateRange = ref([]);
    // 回购日期范围
    const repurchaseDateRange = ref([]);
    // 排除旗帜选择
    const excludeFlags = ref([]);
    // 分析类型选择
    const analysisType = ref('repurchaseCustomers');
    // 动画触发器
    const animationKey = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        selectedProducts.value = [];
        excludeFlags.value = [];
        analysisType.value = 'repurchaseCustomers';

        // 重置付款日期为近七天（不包含当前天）
        const today = dayjs();
        const sevenDaysAgo = today.subtract(7, 'day');
        const yesterday = today.subtract(1, 'day');

        paymentDateRange.value = [sevenDaysAgo, yesterday];
        queryForm.paymentDateBegin = sevenDaysAgo.format('YYYY-MM-DD');
        queryForm.paymentDateEnd = yesterday.format('YYYY-MM-DD');

        // 重置回购日期为近七天（不包含当前天）- 与付款日期保持一致
        repurchaseDateRange.value = [sevenDaysAgo, yesterday];
        queryForm.repurchaseDateBegin = sevenDaysAgo.format('YYYY-MM-DD');
        queryForm.repurchaseDateEnd = yesterday.format('YYYY-MM-DD');

        // 验证并调整回购日期范围，确保在付款日期范围内
        validateAndAdjustRepurchaseDate();

        // 隐藏统计面板
        showStatisticsPanel.value = false;
        if (barChart) {
            barChart.dispose();
            barChart = null;
        }
    }

    // 搜索 - 注意：查询按钮现在直接调用showStatistics函数，此函数保留用于其他可能的搜索逻辑
    function onSearch(){
        queryForm.pageNum = 1;
        // 搜索功能现在主要用于统计分析的筛选条件
        if (showStatisticsPanel.value) {
            // 如果统计面板已显示，重新生成统计数据
            showStatistics();
        }
    }

    // 付款日期变化处理
    function onChangePaymentDate(dates, dateStrings){
        queryForm.paymentDateBegin = dateStrings[0];
        queryForm.paymentDateEnd = dateStrings[1];

        // 当付款日期变化时，检查并调整回购日期范围
        if (dates && dates.length === 2 && repurchaseDateRange.value && repurchaseDateRange.value.length === 2) {
            const paymentStart = dates[0];
            const paymentEnd = dates[1];
            const repurchaseStart = repurchaseDateRange.value[0];
            const repurchaseEnd = repurchaseDateRange.value[1];

            let needAdjust = false;
            let newRepurchaseStart = repurchaseStart;
            let newRepurchaseEnd = repurchaseEnd;

            // 如果回购开始日期早于付款开始日期，调整为付款开始日期
            if (repurchaseStart && repurchaseStart.isBefore(paymentStart, 'day')) {
                newRepurchaseStart = paymentStart;
                needAdjust = true;
            }

            // 如果回购结束日期晚于付款结束日期，调整为付款结束日期
            if (repurchaseEnd && repurchaseEnd.isAfter(paymentEnd, 'day')) {
                newRepurchaseEnd = paymentEnd;
                needAdjust = true;
            }

            // 如果需要调整，更新回购日期
            if (needAdjust) {
                repurchaseDateRange.value = [newRepurchaseStart, newRepurchaseEnd];
                queryForm.repurchaseDateBegin = newRepurchaseStart.format('YYYY-MM-DD');
                queryForm.repurchaseDateEnd = newRepurchaseEnd.format('YYYY-MM-DD');

                // 给用户友好提示
                message.info('回购日期已自动调整到付款日期范围内');
            }
        }

        // 重新添加禁用日期点击监听器，因为付款日期变化可能影响禁用日期范围
        nextTick(() => {
            addDisabledDateClickListener();
        });
    }

    // 回购日期变化处理 - 增强版，包含用户友好提示
    function onChangeRepurchaseDate(dates, dateStrings){
        // 如果用户清空了日期选择，直接处理
        if (!dates || dates.length === 0) {
            queryForm.repurchaseDateBegin = null;
            queryForm.repurchaseDateEnd = null;
            return;
        }

        // 检查选择的日期是否在付款日期范围内
        if (paymentDateRange.value && paymentDateRange.value.length === 2) {
            const paymentStart = paymentDateRange.value[0];
            const paymentEnd = paymentDateRange.value[1];

            if (paymentStart && paymentEnd && dates && dates.length === 2) {
                const repurchaseStart = dates[0];
                const repurchaseEnd = dates[1];
                let hasInvalidDate = false;
                let adjustedStart = repurchaseStart;
                let adjustedEnd = repurchaseEnd;

                // 检查开始日期
                if (repurchaseStart && repurchaseStart.isBefore(paymentStart, 'day')) {
                    message.warning(`回购开始日期不能早于付款开始日期（${paymentStart.format('YYYY-MM-DD')}），已自动调整`);
                    adjustedStart = paymentStart;
                    hasInvalidDate = true;
                }

                // 检查结束日期
                if (repurchaseEnd && repurchaseEnd.isAfter(paymentEnd, 'day')) {
                    message.warning(`回购结束日期不能晚于付款结束日期（${paymentEnd.format('YYYY-MM-DD')}），已自动调整`);
                    adjustedEnd = paymentEnd;
                    hasInvalidDate = true;
                }

                // 如果有无效日期，自动调整并更新
                if (hasInvalidDate) {
                    setTimeout(() => {
                        repurchaseDateRange.value = [adjustedStart, adjustedEnd];
                        queryForm.repurchaseDateBegin = adjustedStart.format('YYYY-MM-DD');
                        queryForm.repurchaseDateEnd = adjustedEnd.format('YYYY-MM-DD');
                    }, 100);
                    return;
                }
            }
        }

        // 如果日期有效，正常更新
        queryForm.repurchaseDateBegin = dateStrings[0];
        queryForm.repurchaseDateEnd = dateStrings[1];
    }

    // 回购日期禁用日期函数 - 限制回购日期必须在付款日期范围内
    function disabledRepurchaseDate(current) {
        if (!paymentDateRange.value || paymentDateRange.value.length !== 2) {
            return false; // 如果付款日期未设置，不禁用任何日期
        }

        const paymentStart = paymentDateRange.value[0];
        const paymentEnd = paymentDateRange.value[1];

        if (!paymentStart || !paymentEnd) {
            return false;
        }

        // 禁用早于付款开始日期或晚于付款结束日期的日期
        return current && (current.isBefore(paymentStart, 'day') || current.isAfter(paymentEnd, 'day'));
    }

    // 验证并调整回购日期范围的辅助函数
    function validateAndAdjustRepurchaseDate() {
        if (!paymentDateRange.value || paymentDateRange.value.length !== 2) {
            return;
        }

        if (!repurchaseDateRange.value || repurchaseDateRange.value.length !== 2) {
            return;
        }

        const paymentStart = paymentDateRange.value[0];
        const paymentEnd = paymentDateRange.value[1];
        const repurchaseStart = repurchaseDateRange.value[0];
        const repurchaseEnd = repurchaseDateRange.value[1];

        let needAdjust = false;
        let newRepurchaseStart = repurchaseStart;
        let newRepurchaseEnd = repurchaseEnd;

        // 如果回购开始日期早于付款开始日期，调整为付款开始日期
        if (repurchaseStart && repurchaseStart.isBefore(paymentStart, 'day')) {
            newRepurchaseStart = paymentStart;
            needAdjust = true;
        }

        // 如果回购结束日期晚于付款结束日期，调整为付款结束日期
        if (repurchaseEnd && repurchaseEnd.isAfter(paymentEnd, 'day')) {
            newRepurchaseEnd = paymentEnd;
            needAdjust = true;
        }

        // 如果需要调整，更新回购日期
        if (needAdjust) {
            repurchaseDateRange.value = [newRepurchaseStart, newRepurchaseEnd];
            queryForm.repurchaseDateBegin = newRepurchaseStart.format('YYYY-MM-DD');
            queryForm.repurchaseDateEnd = newRepurchaseEnd.format('YYYY-MM-DD');
        }
    }

    // 禁用日期点击检测和提示功能
    let clickEventListener = null;

    // 添加禁用日期点击监听器
    function addDisabledDateClickListener() {
        // 移除之前的监听器
        removeDisabledDateClickListener();

        // 等待 DOM 更新后添加监听器
        nextTick(() => {
            if (repurchaseDatePicker.value && repurchaseDatePicker.value.$el) {
                const pickerElement = repurchaseDatePicker.value.$el;

                clickEventListener = function(event) {
                    // 检查点击的是否是日期单元格
                    const clickedCell = event.target.closest('.ant-picker-cell');
                    if (!clickedCell) return;

                    // 检查是否是禁用的日期单元格
                    if (clickedCell.classList.contains('ant-picker-cell-disabled')) {
                        // 获取被点击的日期 - 使用多种方法尝试
                        let clickedDate = null;

                        // 方法1: 从 title 属性获取（最常见）
                        const dateTitle = clickedCell.getAttribute('title');
                        if (dateTitle) {
                            clickedDate = dayjs(dateTitle);
                        }

                        // 方法2: 从单元格内容和面板头部信息构造日期
                        if (!clickedDate || !clickedDate.isValid()) {
                            const cellInner = clickedCell.querySelector('.ant-picker-cell-inner');
                            if (cellInner) {
                                const dayText = cellInner.textContent.trim();
                                if (dayText && !isNaN(dayText)) {
                                    // 获取当前面板显示的年月
                                    const panel = clickedCell.closest('.ant-picker-date-panel');
                                    if (panel) {
                                        const headerView = panel.querySelector('.ant-picker-header-view');
                                        if (headerView) {
                                            const headerText = headerView.textContent;
                                            // 匹配 "2024年1月" 格式
                                            const match = headerText.match(/(\d{4})年(\d{1,2})月/);
                                            if (match) {
                                                const year = match[1];
                                                const month = match[2].padStart(2, '0');
                                                const day = dayText.padStart(2, '0');
                                                clickedDate = dayjs(`${year}-${month}-${day}`);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // 如果成功获取到日期，显示提示
                        if (clickedDate && clickedDate.isValid()) {
                            showDisabledDateTip(clickedDate);
                        } else {
                            // 如果无法确定具体日期，显示通用提示
                            showGenericDisabledDateTip();
                        }
                    }
                };

                // 使用事件委托监听点击事件
                pickerElement.addEventListener('click', clickEventListener, true);
            }
        });
    }

    // 移除禁用日期点击监听器
    function removeDisabledDateClickListener() {
        if (clickEventListener && repurchaseDatePicker.value && repurchaseDatePicker.value.$el) {
            repurchaseDatePicker.value.$el.removeEventListener('click', clickEventListener, true);
            clickEventListener = null;
        }
    }

    // 显示禁用日期点击提示
    function showDisabledDateTip(clickedDate) {
        if (!paymentDateRange.value || paymentDateRange.value.length !== 2) {
            return;
        }

        const paymentStart = paymentDateRange.value[0];
        const paymentEnd = paymentDateRange.value[1];

        if (!paymentStart || !paymentEnd || !clickedDate) {
            return;
        }

        // 使用防抖机制，避免频繁提示
        const now = Date.now();
        if (now - lastTipTime.value < 1000) { // 1秒内不重复提示
            return;
        }
        lastTipTime.value = now;

        // 根据点击的日期位置显示不同的提示
        if (clickedDate.isBefore(paymentStart, 'day')) {
            message.warning(`回购日期不能早于付款开始日期（${paymentStart.format('YYYY-MM-DD')}）`);
        } else if (clickedDate.isAfter(paymentEnd, 'day')) {
            message.warning(`回购日期不能晚于付款结束日期（${paymentEnd.format('YYYY-MM-DD')}）`);
        }
    }

    // 显示通用的禁用日期提示（当无法确定具体日期时）
    function showGenericDisabledDateTip() {
        if (!paymentDateRange.value || paymentDateRange.value.length !== 2) {
            return;
        }

        const paymentStart = paymentDateRange.value[0];
        const paymentEnd = paymentDateRange.value[1];

        if (!paymentStart || !paymentEnd) {
            return;
        }

        // 使用防抖机制，避免频繁提示
        const now = Date.now();
        if (now - lastTipTime.value < 1000) { // 1秒内不重复提示
            return;
        }
        lastTipTime.value = now;

        // 显示通用提示
        message.warning(`回购日期必须在付款日期范围内（${paymentStart.format('YYYY-MM-DD')} 至 ${paymentEnd.format('YYYY-MM-DD')}）`);
    }

    // 防抖控制变量，避免频繁提示
    const lastTipTime = ref(0);

    onUnmounted(() => {
        if (barChart) {
            barChart.dispose();
            barChart = null;
        }

        // 清理禁用日期点击监听器
        removeDisabledDateClickListener();
    });

    // ---------------------------- 商品选择功能 ----------------------------
    const productSelectModalRef = ref();
    const selectedProducts = ref([]);

    // 显示商品选择弹窗
    function showProductSelectModal() {
        productSelectModalRef.value.showModal(queryForm.selectedProductIds);
    }

    // 商品选择确认回调
    function onProductSelected(products) {
        selectedProducts.value = products;
        queryForm.selectedProductIds = products.map(p => p.goodsId);
        message.success(`已选择 ${products.length} 个货品ID`);
    }

    // 计算显示的选中商品文本
    const selectedProductText = computed(() => {
        if (selectedProducts.value.length === 0) {
            return '';
        } else if (selectedProducts.value.length === 1) {
            return selectedProducts.value[0].goodsId;
        } else {
            return `已选择 ${selectedProducts.value.length} 个货品ID`;
        }
    });

    // ---------------------------- 统计分析 ----------------------------
    const statisticsModalRef = ref();
    const showStatisticsPanel = ref(false);
    const statisticsData = ref(null);
    const barChartRef = ref();
    let barChart = null;

    // 表格列定义
    const detailColumns = ref([
        {
            title: '复购次数',
            dataIndex: 'repurchaseTimes',
            key: 'repurchaseTimes',
            width: 100,
            align: 'center',
            customHeaderCell: () => ({
                style: { textAlign: 'center' }
            })
        },
        {
            title: '复购人数',
            dataIndex: 'repurchaseCustomers',
            key: 'repurchaseCustomers',
            width: 100,
            align: 'center',
            customHeaderCell: () => ({
                style: { textAlign: 'center' }
            })
        },
        {
            title: '复购件数',
            dataIndex: 'repurchaseQuantity',
            key: 'repurchaseQuantity',
            width: 100,
            align: 'center',
            customHeaderCell: () => ({
                style: { textAlign: 'center' }
            })
        },
        {
            title: '复购金额',
            dataIndex: 'repurchaseAmount',
            key: 'repurchaseAmount',
            width: 120,
            align: 'center',
            customHeaderCell: () => ({
                style: { textAlign: 'center' }
            })
        },
        {
            title: '平均复购周期天数',
            dataIndex: 'avgRepurchaseCycleDays',
            key: 'avgRepurchaseCycleDays',
            width: 140,
            align: 'center',
            customHeaderCell: () => ({
                style: { textAlign: 'center' }
            })
        },
    ]);

    // 复购明细数据
    const detailData = ref([]);

    // 数字格式化方法
    function formatNumber(value) {
        if (value === null || value === undefined || value === '') {
            return '-';
        }
        const num = parseFloat(value);
        if (isNaN(num)) {
            return value;
        }
        // 如果是整数，显示为整数；如果有小数，保留2位小数
        return num % 1 === 0 ? num.toString() : num.toFixed(2);
    }

    // 计算复购率
    const repurchaseRate = computed(() => {
        const stats = statisticsData.value;
        if (!stats || !stats.paymentUserCount) {
            return 0;
        }
        return stats.repurchaseRate || 0;
    });

    // 环形进度样式
    const circleProgressStyle = computed(() => ({
        background: `conic-gradient(#52c41a ${repurchaseRate.value}%, #f0f2f5 ${repurchaseRate.value}%)`,
    }));

    // 显示统计分析面板
    async function showStatistics() {
        // 校验：必须选择货品ID
        if (!queryForm.selectedProductIds || queryForm.selectedProductIds.length === 0) {
            message.warning('请先选择货品ID后再进行统计分析');
            return;
        }
        // 校验：必须选择付款日期
        if (!paymentDateRange.value || paymentDateRange.value.length !== 2 || !paymentDateRange.value[0] || !paymentDateRange.value[1]) {
            message.warning('请先选择付款日期范围后再进行统计分析');
            return;
        }

        showStatisticsPanel.value = true;
        SmartLoading.show();
        try {
            const params = {
                platformGoodsIds: queryForm.selectedProductIds,
                startDate: queryForm.paymentDateBegin,
                endDate: queryForm.paymentDateEnd,
                customerType: queryForm.customerType,
                excludeFlag: excludeFlags.value.join(','),
            };
            const response = await customerLinkApi.queryAnalysis(params);
            statisticsData.value = response.data;

            // 触发复购率圆形进度条动画重新播放
            animationKey.value++;

            // 获取复购明细数据
            const detailResponse = await customerLinkApi.queryDetail(params);

            // 对数据进行排序处理，确保人均行在合计行上面
            const rawData = detailResponse.data || [];
            const sortedData = rawData.sort((a, b) => {
                // 定义排序权重
                const getSortWeight = (times) => {
                    if (times && times.includes('第') && times.includes('次')) {
                        const num = parseInt(times.replace('第', '').replace('次', ''));
                        return isNaN(num) ? 0 : num;
                    }
                    if (times === '人均') return 998;
                    if (times === '合计') return 999;
                    if (times === '均值') return 1000;
                    return 0;
                };

                const aWeight = getSortWeight(a.repurchaseTimes);
                const bWeight = getSortWeight(b.repurchaseTimes);

                return aWeight - bWeight;
            });

            detailData.value = sortedData;

            // 更新图表
            nextTick(() => {
                drawBarChart();
            });

        } catch (e) {
            console.error('统计分析请求失败:', e);
            message.error('获取统计数据失败，请稍后重试');
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    function hideStatistics() {
        showStatisticsPanel.value = false;
        if (barChart) {
            barChart.dispose();
            barChart = null;
        }
    }

    // 绘制柱状图
    function drawBarChart() {
        if (!detailData.value || detailData.value.length === 0) {
            return;
        }

        // 过滤掉合计、均值和人均行，只显示实际的复购次数数据
        const chartData = detailData.value.filter(item =>
            item.repurchaseTimes !== '合计' &&
            item.repurchaseTimes !== '均值' &&
            item.repurchaseTimes !== '人均'
        );

        if (barChart) {
            barChart.dispose();
        }
        barChart = echarts.init(barChartRef.value);

        // 根据选择的分析类型获取对应的数据和配置
        const getChartConfig = () => {
            switch (analysisType.value) {
                case 'repurchaseCustomers':
                    return {
                        yAxisName: '复购人数',
                        seriesName: '复购人数',
                        dataKey: 'repurchaseCustomers',
                        unit: '人'
                    };
                case 'repurchaseQuantity':
                    return {
                        yAxisName: '复购件数',
                        seriesName: '复购件数',
                        dataKey: 'repurchaseQuantity',
                        unit: '件'
                    };
                case 'repurchaseAmount':
                    return {
                        yAxisName: '复购金额',
                        seriesName: '复购金额',
                        dataKey: 'repurchaseAmount',
                        unit: '元'
                    };
                case 'avgRepurchaseCycle':
                    return {
                        yAxisName: '平均复购周期',
                        seriesName: '平均复购周期',
                        dataKey: 'avgRepurchaseCycleDays',
                        unit: '天'
                    };
                default:
                    return {
                        yAxisName: '复购人数',
                        seriesName: '复购人数',
                        dataKey: 'repurchaseCustomers',
                        unit: '人'
                    };
            }
        };

        const config = getChartConfig();

        const option = {
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const data = params[0];
                    const originalData = chartData[data.dataIndex];
                    return `${data.name}<br/>${config.seriesName}: ${data.value}${config.unit}<br/>复购人数: ${originalData.repurchaseCustomers}人<br/>复购件数: ${originalData.repurchaseQuantity}件<br/>复购金额: ${originalData.repurchaseAmount}元<br/>平均复购周期: ${originalData.avgRepurchaseCycleDays}天`;
                }
            },
            grid: {
                left: '10%',
                right: '10%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: chartData.map(item => item.repurchaseTimes),
                axisLabel: {
                    fontSize: 12,
                    rotate: 45 // 旋转标签以避免重叠
                }
            },
            yAxis: {
                type: 'value',
                name: config.yAxisName,
                axisLabel: {
                    fontSize: 12
                },
                // 设置Y轴的最大值，确保柱子不会超出容器
                max: function(value) {
                    return Math.ceil(value.max * 1.2);
                }
            },
            series: [{
                name: config.seriesName,
                type: 'bar',
                data: chartData.map(item => parseFloat(item[config.dataKey]) || 0),
                barWidth: '50%',
                label: {
                    show: true,
                    position: 'top',
                    formatter: '{c}',
                    fontSize: 12
                },
                itemStyle: {
                    color: '#1890ff'
                }
            }]
        };

        barChart.setOption(option);
    }

    // ------------------- 查询和重置 -------------------
    async function queryData() {
        // 由于页面已移除表格，此函数保留但内容可清空或用作他用
    }

    // ------------------- 下载功能 -------------------
    // 下载复购明细订单数据
    async function downloadDetail() {
        // 校验：必须选择货品ID
        if (!queryForm.selectedProductIds || queryForm.selectedProductIds.length === 0) {
            message.warning('请先选择货品ID后再下载明细');
            return;
        }
        // 校验：必须选择付款日期
        if (!paymentDateRange.value || paymentDateRange.value.length !== 2 || !paymentDateRange.value[0] || !paymentDateRange.value[1]) {
            message.warning('请先选择付款日期范围后再下载明细');
            return;
        }

        try {
            SmartLoading.show();

            const params = {
                platformGoodsIds: queryForm.selectedProductIds,
                startDate: queryForm.paymentDateBegin,
                endDate: queryForm.paymentDateEnd,
                customerType: queryForm.customerType,
                excludeFlag: excludeFlags.value.join(','),
            };

            // 调用下载API
            const response = await customerLinkApi.downloadDetail(params);

            // 手动处理下载
            const blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            // 从响应头获取文件名
            let fileName = '复购明细订单数据.xlsx';
            const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition'];
            if (contentDisposition) {
                const fileNameMatch = contentDisposition.match(/filename=([^;]+)/);
                if (fileNameMatch) {
                    fileName = decodeURIComponent(fileNameMatch[1]);
                }
            }

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            message.success('下载成功');
        } catch (e) {
            console.error('下载请求失败:', e);
            message.error('下载失败，请稍后重试');
        } finally {
            SmartLoading.hide();
        }
    }

    // ------------------- 监听器 -------------------
    // 监听分析类型变化，重新绘制图表
    watch(analysisType, () => {
        if (showStatisticsPanel.value && detailData.value.length > 0) {
            nextTick(() => {
                drawBarChart();
            });
        }
    });

    // 页面初始化
    onMounted(() => {
        // 设置默认付款日期为近七天（不包含当前天）
        const today = dayjs();
        const sevenDaysAgo = today.subtract(7, 'day');
        const yesterday = today.subtract(1, 'day');

        paymentDateRange.value = [sevenDaysAgo, yesterday];

        // 同步到查询表单
        queryForm.paymentDateBegin = sevenDaysAgo.format('YYYY-MM-DD');
        queryForm.paymentDateEnd = yesterday.format('YYYY-MM-DD');

        // 设置默认回购日期为近七天（不包含当前天）- 与付款日期保持一致
        repurchaseDateRange.value = [sevenDaysAgo, yesterday];
        queryForm.repurchaseDateBegin = sevenDaysAgo.format('YYYY-MM-DD');
        queryForm.repurchaseDateEnd = yesterday.format('YYYY-MM-DD');

        // 验证并调整回购日期范围，确保在付款日期范围内
        validateAndAdjustRepurchaseDate();

        // 添加禁用日期点击监听器
        addDisabledDateClickListener();
    });

</script>

<style lang="less" scoped>
    .statistics-panel {
        margin-bottom: 16px;
        
        .statistics-card {
            :deep(.ant-card-body) {
                padding: 16px;
            }
            
                         .metrics-row {
                 margin-bottom: 16px;
                
                                 .metric-card {
                     padding: 16px;
                     background: #fff;
                     border: 1px solid #e8e8e8;
                     border-radius: 6px;
                     text-align: center;
                     transition: all 0.3s;
                    
                    &:hover {
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    }
                    
                    &.primary {
                        border-color: #1890ff;
                        background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
                    }
                    
                                         .metric-header {
                         display: flex;
                         align-items: center;
                         justify-content: center;
                         margin-bottom: 8px;
                        
                        .metric-title {
                            font-size: 14px;
                            color: #666;
                            margin-right: 4px;
                        }
                        
                        .help-icon {
                            color: #999;
                            font-size: 12px;
                            cursor: help;

                            &:hover {
                                color: #1890ff;
                            }
                        }
                    }
                    
                                         .metric-value {
                         font-size: 24px;
                         font-weight: bold;
                         color: #333;
                         line-height: 1;
                         margin-bottom: 2px;
                        
                        &.primary {
                            color: #1890ff;
                        }
                    }
                    
                    .metric-unit {
                        font-size: 12px;
                        color: #999;
                    }
                }
            }
            
                         .charts-row {
                 margin-bottom: 16px;
                
                                 .chart-container {
                     h4 {
                         margin: 0 0 12px 0;
                         font-size: 15px;
                         font-weight: 600;
                         color: #333;

                         .help-icon {
                             margin-left: 4px;
                             color: #999;
                             font-size: 12px;
                             cursor: help;

                             &:hover {
                                 color: #1890ff;
                             }
                         }
                     }
                    
                                                              .repurchase-analysis {
                         display: flex;
                         align-items: center;
                         justify-content: center;
                         gap: 40px;
                         padding: 20px;
                         
                         .circle-progress {
                             .progress-circle {
                                 width: 160px;
                                 height: 160px;
                                 border-radius: 50%;
                                 display: flex;
                                 align-items: center;
                                 justify-content: center;
                                 position: relative;
                                 transition: all 0.6s ease-in-out;
                                 animation: progressRotate 2s ease-in-out;
                                 
                                 &::before {
                                     content: '';
                                     position: absolute;
                                     width: 120px;
                                     height: 120px;
                                     background: #fff;
                                     border-radius: 50%;
                                     box-shadow: inset 0 0 10px rgba(0,0,0,0.1);
                                 }
                                 
                                 .progress-text {
                                     position: relative;
                                     z-index: 1;
                                     text-align: center;
                                     animation: fadeInUp 1s ease-out 0.5s both;
                                     
                                     .percentage {
                                         font-size: 32px;
                                         font-weight: bold;
                                         color: #1890ff;
                                         line-height: 1;
                                         text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                                     }
                                     
                                     .label {
                                         font-size: 14px;
                                         color: #666;
                                         margin-top: 4px;
                                         font-weight: 500;
                                     }
                                 }
                             }
                         }
                         
                         .stats-side {
                             flex: 1;
                             max-width: 200px;
                             
                             .stat-row {
                                 display: flex;
                                 align-items: center;
                                 margin-bottom: 16px;
                                 padding: 12px 16px;
                                 background: #f8f9fa;
                                 border-radius: 8px;
                                 transition: all 0.3s ease;
                                 animation: slideInRight 0.6s ease-out;
                                 
                                 &:hover {
                                     background: #e6f7ff;
                                     transform: translateX(4px);
                                 }
                                 
                                 &:nth-child(1) {
                                     animation-delay: 0.2s;
                                 }
                                 
                                 &:nth-child(2) {
                                     animation-delay: 0.4s;
                                 }
                                 
                                 .stat-label {
                                     font-size: 14px;
                                     color: #666;
                                     margin-right: 12px;
                                     min-width: 70px;
                                     font-weight: 500;
                                     display: flex;
                                     align-items: center;

                                     .help-icon {
                                         margin-left: 4px;
                                         color: #999;
                                         font-size: 12px;
                                         cursor: help;

                                         &:hover {
                                             color: #1890ff;
                                         }
                                     }
                                 }
                                 
                                 .stat-dot {
                                     width: 10px;
                                     height: 10px;
                                     border-radius: 50%;
                                     margin-right: 12px;
                                     animation: pulse 2s infinite;
                                     
                                     &.repurchase {
                                         background: #1890ff;
                                         box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
                                     }
                                     
                                     &.single {
                                         background: #d9d9d9;
                                         box-shadow: 0 0 0 0 rgba(217, 217, 217, 0.7);
                                     }
                                 }
                                 
                                 .stat-value {
                                     font-size: 16px;
                                     color: #333;
                                     font-weight: 600;
                                 }
                             }
                         }
                     }
                    
                                         .bar-chart {
                         width: 100%;
                         height: 220px;
                         min-height: 220px;
                     }
                }
            }
            
            .detail-section {
                                 .section-header {
                     display: flex;
                     justify-content: space-between;
                     align-items: center;
                     margin-bottom: 12px;
                    
                                         h4 {
                         margin: 0;
                         font-size: 15px;
                         font-weight: 600;
                         color: #333;
                     }
                }
                
                .detail-table {
                    :deep(.ant-table-thead > tr > th) {
                        background: #fafafa;
                        font-weight: 600;
                    }
                }
                         }
         }
     }
     
     .empty-state {
         padding: 60px 20px;
         text-align: center;
         background: #fafafa;
         border-radius: 8px;
         margin: 20px 0;
     }
     
     // 动画关键帧
     @keyframes progressRotate {
         0% {
             transform: rotate(-90deg);
             opacity: 0;
         }
         50% {
             opacity: 0.8;
         }
         100% {
             transform: rotate(0deg);
             opacity: 1;
         }
     }
     
     @keyframes fadeInUp {
         0% {
             opacity: 0;
             transform: translateY(20px);
         }
         100% {
             opacity: 1;
             transform: translateY(0);
         }
     }
     
     @keyframes slideInRight {
         0% {
             opacity: 0;
             transform: translateX(-30px);
         }
         100% {
             opacity: 1;
             transform: translateX(0);
         }
     }
     
     @keyframes pulse {
         0% {
             box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
         }
         70% {
             box-shadow: 0 0 0 6px rgba(24, 144, 255, 0);
         }
         100% {
             box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
         }
     }

     // 排除旗帜样式
     .exclude-flags-container {
         display: inline-flex;
         align-items: center;
         margin-left: 16px;
         padding: 8px 0;

         .exclude-label {
             font-size: 13px;
             color: #666;
             margin-right: 12px;
             font-weight: 500;
         }

         .flag-checkbox-group {
             display: flex;
             gap: 12px;

             .flag-checkbox {
                 margin: 0;

                 :deep(.ant-checkbox-wrapper) {
                     display: flex;
                     align-items: center;
                     padding: 4px 6px;
                     border-radius: 4px;
                     transition: all 0.2s;
                     cursor: pointer;

                     &:hover {
                         background: rgba(0, 0, 0, 0.04);
                     }
                 }

                 :deep(.ant-checkbox-wrapper-checked) {
                     background: rgba(24, 144, 255, 0.1);
                 }

                 :deep(.ant-checkbox) {
                     margin-right: 8px;
                     display: flex;
                     align-items: center;
                 }

                 :deep(.ant-checkbox-inner) {
                     width: 14px;
                     height: 14px;
                 }

                 .flag-icon {
                     width: 16px;
                     height: 12px;
                     position: relative;
                     cursor: pointer;
                     transition: all 0.2s;
                     display: inline-block;
                     vertical-align: middle;

                     // 旗杆
                     &::before {
                         content: '';
                         position: absolute;
                         left: -2px;
                         top: -2px;
                         width: 2px;
                         height: 16px;
                         background: #666;
                         border-radius: 1px;
                     }

                     // 旗帜主体
                     &::after {
                         content: '';
                         position: absolute;
                         left: 0;
                         top: 0;
                         width: 16px;
                         height: 10px;
                         clip-path: polygon(0% 0%, 85% 0%, 100% 50%, 85% 100%, 0% 100%);
                         transition: all 0.2s;
                     }

                     &.red-flag::after {
                         background: #ff4d4f;
                     }

                     &.blue-flag::after {
                         background: #1890ff;
                     }

                     &.green-flag::after {
                         background: #52c41a;
                     }

                     &.yellow-flag::after {
                         background: #fadb14;
                     }

                     &.purple-flag::after {
                         background: #722ed1;
                     }

                     &.gray-flag::after {
                         background: #8c8c8c;
                     }

                     &:hover::after {
                         transform: scale(1.1);
                         box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                     }
                 }
             }
         }

     }

     // 右侧控制区域
     .right-controls {
         display: flex;
         align-items: center;
         gap: 24px;

         .analysis-type-group {
             padding: 8px 0;

             :deep(.ant-radio-wrapper) {
                 margin-right: 16px;
                 font-size: 13px;
                 color: #666;
                 padding: 4px 8px;
                 border-radius: 4px;
                 transition: all 0.2s;
                 
                 &:hover {
                     color: #1890ff;
                     background: rgba(24, 144, 255, 0.04);
                 }
             }

             :deep(.ant-radio-wrapper-checked) {
                 color: #1890ff;
                 font-weight: 500;
                 background: rgba(24, 144, 255, 0.1);
             }

             :deep(.ant-radio) {
                 margin-right: 8px;
             }

             :deep(.ant-radio-inner) {
                 width: 14px;
                 height: 14px;
             }
         }
     }

     // 复购明细表格样式
     .detail-table {
         :deep(.ant-table-thead > tr > th) {
             text-align: center;
             font-weight: 600;
             background: #fafafa;
             border-bottom: 2px solid #e8e8e8;
         }

         :deep(.ant-table-tbody > tr > td) {
             text-align: center;
         }

         // 合计、均值和人均行字体加粗
         :deep(.summary-text) {
             font-weight: 600;
        }

        // 人均和合计行整行字体加粗
        :deep(.summary-row td) {
            font-weight: 600;
         }

         // 表格整体样式
         :deep(.ant-table) {
             border: 1px solid #e8e8e8;
             border-radius: 6px;
         }

         :deep(.ant-table-container) {
             border-radius: 6px;
         }

         // 表格标题中的help-icon样式
         :deep(.ant-table-thead .help-icon) {
             margin-left: 4px;
             color: #999;
             font-size: 12px;
             cursor: help;

             &:hover {
                 color: #1890ff;
             }
         }
     }
</style>