# 链接视角页面数据库结构文档

## 📋 概述

本文档记录了链接视角页面的数据库结构、查询逻辑和实现方案。链接视角页面主要用于分析客户的复购行为和链接效果。

**最后更新时间**: 2025-07-30  
**版本**: v2.0 (实时计算版本)

## 🏗️ 架构变更历史

### v1.0 (已废弃)
- 使用 `smart_admin_v3.crm_客户查询` 表存储预计算的客户数据
- 通过定时任务同步数据，存在数据滞后问题
- 客户分类逻辑依赖同步表中的 `客户类型` 字段

### v2.0 (当前版本)
- 直接从 `lirun` 数据库实时计算客户数据
- 移除对同步表的依赖，解决数据滞后问题
- 实现真正的实时数据分析

## 🗄️ 数据库结构

### 主要数据源

#### lirun.订单明细 (核心数据表)
```sql
-- 关键字段说明
平台货品ID        VARCHAR    -- 商品唯一标识
客户唯一编码      VARCHAR    -- 客户唯一标识  
付款时间         DATETIME   -- 订单付款时间
付款金额         DECIMAL    -- 实际付款金额
已付            DECIMAL    -- 已付金额
订单状态         VARCHAR    -- 订单状态
订单来源         VARCHAR    -- 订单来源
标记名称         VARCHAR    -- 订单标记
```

### 数据过滤条件
```sql
-- 有效订单过滤条件
WHERE om.订单状态 != '线下退款'
  AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
  AND om.标记名称 != '贴贴贴贴贴'
```

## 🔍 核心查询逻辑

### 1. 客户分类逻辑 (新客/老客)

```sql
-- 实时客户分类计算
CASE 
    WHEN EXISTS (
        SELECT 1 FROM lirun.订单明细 hist 
        WHERE hist.客户唯一编码 = fo.客户唯一编码 
            AND hist.付款时间 < #{queryForm.startDate}
            AND hist.订单状态 != '线下退款'
            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
            AND hist.标记名称 != '贴贴贴贴贴'
    ) THEN '老客'
    ELSE '新客'
END as 客户类型
```

**分类规则**:
- **新客**: 在查询开始日期之前没有购买记录的客户
- **老客**: 在查询开始日期之前有购买记录的客户

### 2. 复购分析逻辑

```sql
-- 复购客户识别
SELECT 客户唯一编码
FROM lirun.订单明细 om
WHERE [基础过滤条件]
GROUP BY 客户唯一编码
HAVING COUNT(DISTINCT DATE(om.付款时间)) > 1
```

**复购定义**: 在指定时间范围内有多个不同购买日期的客户

### 3. 复购周期计算

```sql
-- 平均复购周期计算
SELECT 
    AVG(DATEDIFF(next_order_date, current_order_date)) as 平均复购周期
FROM (
    SELECT 
        客户唯一编码,
        付款时间 as current_order_date,
        LEAD(付款时间) OVER (
            PARTITION BY 客户唯一编码 
            ORDER BY 付款时间
        ) as next_order_date
    FROM lirun.订单明细
    WHERE [过滤条件]
) t
WHERE next_order_date IS NOT NULL
```

## 📊 主要查询接口

### 1. queryAnalysis - 链接分析统计

**功能**: 提供复购率、复购人数、付款人数等核心指标

**关键CTE结构**:
```sql
-- CTE 1: 基础订单过滤
FilteredOrders AS (...)

-- CTE 1.5: 实时客户分类
FilteredOrdersWithCustomerType AS (...)

-- CTE 2: 客户汇总数据  
CustomerSummary AS (...)

-- CTE 3: 复购客户识别
RepeatCustomers AS (...)

-- CTE 4: 复购周期计算
RepeatCycles AS (...)
```

### 2. queryDetail - 复购明细

**功能**: 提供按复购次数分组的详细数据

**输出字段**:
- 复购次数
- 复购人数  
- 复购件数
- 复购金额
- 平均复购周期天数

### 3. downloadOrderDetail - 订单明细导出

**功能**: 导出符合条件的订单明细数据

**输出字段**: 订单的完整明细信息

## 🎯 查询参数

### CustomerLinkQueryForm 参数说明

```java
public class CustomerLinkQueryForm {
    private String customerType;    // 客户类型: "不限", "新客", "老客"
    private Date startDate;         // 付款开始日期
    private Date endDate;           // 付款结束日期
    private Date repurchaseDateBegin; // 回购开始日期
    private Date repurchaseDateEnd;   // 回购结束日期
    private String productId;       // 货品ID
    private List<String> excludeFlags; // 排除旗帜列表
}
```

### 参数映射到SQL

```sql
-- 客户类型过滤
<if test="queryForm.customerType == '新客'">
    AND NOT EXISTS (历史购买记录查询)
</if>
<if test="queryForm.customerType == '老客'">
    AND EXISTS (历史购买记录查询)
</if>

-- 付款时间范围过滤
AND om.付款时间 >= #{queryForm.startDate}
AND om.付款时间 < #{queryForm.endDate}

-- 回购时间范围过滤（用于复购分析）
<if test="queryForm.repurchaseDateBegin != null">
    AND om.付款时间 >= #{queryForm.repurchaseDateBegin}
</if>
<if test="queryForm.repurchaseDateEnd != null">
    AND om.付款时间 <= #{queryForm.repurchaseDateEnd}
</if>

-- 货品ID过滤
<if test="queryForm.productId != null and queryForm.productId != ''">
    AND om.平台货品ID = #{queryForm.productId}
</if>
```

## ⚡ 性能优化

### 1. 索引建议

```sql
-- lirun.订单明细表建议索引
CREATE INDEX idx_product_payment_time ON 订单明细(平台货品ID, 付款时间);
CREATE INDEX idx_customer_payment_time ON 订单明细(客户唯一编码, 付款时间);
CREATE INDEX idx_payment_time ON 订单明细(付款时间);
```

### 2. 查询优化要点

- 使用CTE分步处理，提高可读性和性能
- EXISTS子查询用于客户分类，避免大表JOIN
- 合理使用索引覆盖查询
- 时间范围查询使用半开区间 `[start, end)`

## 🗑️ 已废弃的表结构

### smart_admin_v3.crm_客户查询 (已删除)
```sql
-- 此表已不再使用，可以安全删除
-- 原用途: 存储预计算的客户汇总数据
-- 废弃原因: 数据同步滞后，改为实时计算
```

### smart_admin_v3.t_customer_classification_log (已删除)  
```sql
-- 此表已不再使用，可以安全删除
-- 原用途: 记录客户分类任务执行日志
-- 废弃原因: 不再需要预计算任务
```

## 🔧 配置文件

### 数据源配置 (sa-base.yaml)
```yaml
spring:
  datasource:
    # 主数据源 - smart_admin_v3
    url: ******************************************
    
    # 外部数据源 - lirun (用于链接视角)
    external:
      url: *********************************
```

### MyBatis映射文件
- 位置: `smart-admin-api-java17-springboot3/sa-admin/src/main/resources/mapper/business/customer/CustomerLinkMapper.xml`
- 包含: queryAnalysis, queryDetail, downloadOrderDetail 三个主要查询

## 📈 数据验证示例

### 验证查询准确性
```sql
-- 验证付款人数
SELECT COUNT(DISTINCT 客户唯一编码) as 付款人数
FROM lirun.订单明细 om
WHERE om.平台货品ID = '779818444472'
    AND om.付款时间 >= '2025-07-23'
    AND om.付款时间 < '2025-07-30'
    AND om.订单状态 != '线下退款'
    AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
    AND om.标记名称 != '贴贴贴贴贴';

-- 验证复购人数  
SELECT COUNT(*) as 复购人数
FROM (
    SELECT 客户唯一编码
    FROM lirun.订单明细 om
    WHERE [同上过滤条件]
    GROUP BY 客户唯一编码
    HAVING COUNT(DISTINCT DATE(om.付款时间)) > 1
) t;
```

## 🚀 部署说明

1. **数据库连接**: 确保应用能同时连接 smart_admin_v3 和 lirun 数据库
2. **索引创建**: 在 lirun.订单明细 表上创建建议的索引
3. **配置更新**: 更新数据源配置文件
4. **代码部署**: 部署包含新查询逻辑的 CustomerLinkMapper.xml

## 📊 字段统计逻辑详解

### 核心统计指标

#### 1. 付款人数 (paymentUserCount)
**定义**: 在指定时间范围内，购买了筛选货品的去重客户数量。

**计算逻辑**:
- 从订单明细表中筛选出符合条件的订单记录
- 应用时间范围过滤：付款时间在查询开始日期和结束日期之间
- 应用货品过滤：平台货品ID在选中的货品列表中
- 应用订单状态过滤：排除线下退款、手工创建已取消、特定标记的订单
- 应用客服标旗过滤：排除用户选择的标旗订单
- 对客户唯一编码进行去重计数

#### 2. 复购人数 (repurchaseUserCount)
**定义**: 在指定时间范围内，对筛选货品有多天购买记录的客户数量。

**计算逻辑**:
- 基于付款人数的筛选条件，进一步分析每个客户的购买行为
- 按客户唯一编码分组，统计每个客户的购买天数
- 购买天数计算方式：对付款时间按日期去重后计数
- 筛选出购买天数大于等于2天的客户
- 对这些客户进行计数得到复购人数

#### 3. 复购率 (repurchaseRate)
**定义**: 复购客户占总付款客户的百分比，反映客户重复购买的比例。

**计算逻辑**:
- 复购率 = (复购人数 ÷ 付款人数) × 100%
- 结果保留两位小数
- 当付款人数为0时，复购率显示为0%

#### 4. 平均复购周期 (avgRepurchaseCycle)
**定义**: 复购客户从首次购买到最后一次购买的平均天数，反映客户复购的整体时间跨度。

**计算逻辑**:
- 仅针对复购客户（购买天数≥2）进行计算
- 对每个复购客户，计算其首次购买日期到最后一次购买日期的天数差
- 将所有复购客户的购买周期求平均值
- 结果保留两位小数

#### 5. 平均复购间隔 (avgRepurchaseInterval)
**定义**: 复购客户相邻两次购买之间的平均间隔天数，反映客户复购的频率。

**计算逻辑**:
- 仅针对复购客户的复购行为（排除首次购买）进行计算
- 对每个复购客户，按购买时间排序，计算相邻购买日期的间隔天数
- 将所有复购间隔求平均值
- 结果保留两位小数

#### 6. 最小复购间隔 (minRepurchaseInterval)
**定义**: 所有复购行为中，相邻两次购买间隔的最小天数。

**计算逻辑**:
- 计算所有复购客户的相邻购买间隔
- 取其中的最小值
- 结果为整数天数

#### 7. 最大复购间隔 (maxRepurchaseInterval)
**定义**: 所有复购行为中，相邻两次购买间隔的最大天数。

**计算逻辑**:
- 计算所有复购客户的相邻购买间隔
- 取其中的最大值
- 结果为整数天数

### 复购明细统计指标

#### 8. 复购次数分类 (repurchaseTimes)
**定义**: 按客户的复购次数进行分类统计。

**分类逻辑**:
- 第0次：在查询时间范围内只有1天购买记录的客户（非复购客户）
- 第1次：在查询时间范围内有2天购买记录的客户（复购1次）
- 第2次：在查询时间范围内有3天购买记录的客户（复购2次）
- 第N次：在查询时间范围内有N+1天购买记录的客户（复购N次）
- 合计：所有复购客户的汇总数据
- 人均：复购数据的人均值

#### 9. 各复购次数的人数 (repurchaseCustomers)
**定义**: 每个复购次数分类下的客户数量。

**计算逻辑**:
- 按复购次数分组，统计每组的去重客户数量
- 第0次：统计只有1天购买记录的客户数
- 第N次：统计有N+1天购买记录的客户数
- 合计：所有复购客户的总数

#### 10. 各复购次数的件数 (repurchaseQuantity)
**定义**: 每个复购次数分类下的商品购买件数。

**计算逻辑**:
- 第0次：只有1天购买记录客户的总购买件数
- 第N次：有N+1天购买记录客户的复购件数（排除首次购买件数）
- 合计：所有复购行为的总件数
- 人均：复购总件数除以复购客户数

#### 11. 各复购次数的金额 (repurchaseAmount)
**定义**: 每个复购次数分类下的购买金额。

**计算逻辑**:
- 第0次：只有1天购买记录客户的总购买金额
- 第N次：有N+1天购买记录客户的复购金额（排除首次购买金额）
- 合计：所有复购行为的总金额
- 人均：复购总金额除以复购客户数
- 结果保留两位小数

#### 12. 各复购次数的平均复购周期 (avgRepurchaseCycleDays)
**定义**: 每个复购次数分类下客户的平均复购间隔天数。

**计算逻辑**:
- 第0次：无复购行为，显示为空
- 第N次：该分类下所有复购间隔的平均值
- 合计：所有复购间隔的总平均值
- 人均：与合计相同
- 结果保留两位小数

### 客户分类逻辑

#### 13. 新客老客判断 (客户类型)
**定义**: 基于客户的历史购买记录判断其身份属性。

**判断逻辑**:
- 以查询开始日期为分界点
- 新客：在查询开始日期之前，该客户在系统中没有任何有效购买记录
- 老客：在查询开始日期之前，该客户在系统中已有有效购买记录
- 历史记录查询使用相同的订单过滤条件
- 注意：判断依据是客户的整体购买历史，不限于当前筛选的货品ID

### 数据过滤规则

#### 14. 有效订单定义
**过滤条件**:
- 订单状态不等于"线下退款"
- 排除订单来源为"手工创建"且订单状态为"已取消"的记录
- 排除标记名称为"贴贴贴贴贴"的记录
- 根据用户选择排除特定客服标旗的记录
- 货品ID、客户编码、付款时间等关键字段不能为空

#### 15. 时间范围处理
**处理逻辑**:
- 查询开始日期：包含该日期的00:00:00时刻
- 查询结束日期：不包含该日期，即到前一天的23:59:59时刻
- 使用半开区间设计，便于索引优化和边界处理

#### 16. 购买天数计算
**计算方式**:
- 将付款时间按日期部分去重
- 同一天内的多次购买只计算为1天
- 购买天数 = 去重后的购买日期数量
- 复购次数 = 购买天数 - 1

这些统计逻辑确保了数据的准确性和一致性，为业务分析提供了可靠的数据基础。

## � 回购日期筛选功能

### 功能概述
链接视角页面新增回购日期筛选功能，允许用户在付款日期基础上进一步限定回购行为的时间范围，提供更精确的复购分析。

### 功能特性

#### 1. 双重日期筛选
- **付款日期**: 定义整体分析的时间窗口
- **回购日期**: 在付款日期范围内进一步限定回购行为的时间范围
- **智能约束**: 回购日期范围自动限制在付款日期范围内

#### 2. 日期范围约束逻辑
```javascript
// 回购日期禁用逻辑
function disabledRepurchaseDate(current) {
    if (!paymentDateRange.value || paymentDateRange.value.length !== 2) {
        return true; // 未选择付款日期时，禁用所有回购日期
    }

    const paymentStart = paymentDateRange.value[0];
    const paymentEnd = paymentDateRange.value[1];

    // 回购日期必须在付款日期范围内
    return current && (
        current.isBefore(paymentStart, 'day') ||
        current.isAfter(paymentEnd, 'day')
    );
}
```

#### 3. 智能默认值同步
- **自动同步**: 选择付款日期后，回购日期自动设置为相同范围
- **实时调整**: 付款日期变更时，回购日期自动调整到新的有效范围内
- **边界处理**: 确保回购日期始终在付款日期范围内

#### 4. 用户交互优化

##### 精确点击检测
- **禁用日期点击检测**: 实现对禁用日期的精确点击检测
- **事件委托机制**: 使用DOM事件委托监听日期选择器内的点击事件
- **智能日期解析**: 多种方法解析被点击的日期（title属性、DOM结构解析）

##### 智能提示系统
```javascript
// 根据点击的禁用日期位置显示不同提示
function showDisabledDateTip(clickedDate) {
    const paymentStart = paymentDateRange.value[0];
    const paymentEnd = paymentDateRange.value[1];

    if (clickedDate.isBefore(paymentStart, 'day')) {
        message.warning(`回购日期不能早于付款开始日期（${paymentStart.format('YYYY-MM-DD')}）`);
    } else if (clickedDate.isAfter(paymentEnd, 'day')) {
        message.warning(`回购日期不能晚于付款结束日期（${paymentEnd.format('YYYY-MM-DD')}）`);
    }
}
```

##### 生命周期管理
- **事件监听器管理**: 组件挂载时添加，卸载时移除
- **防抖机制**: 避免重复提示消息
- **资源清理**: 确保无内存泄漏

### 技术实现

#### 1. 前端组件结构
```vue
<template>
  <a-form-item label="付款日期" class="smart-query-form-item">
    <a-range-picker
      v-model:value="paymentDateRange"
      @change="onChangePaymentDate"
      :presets="defaultTimeRanges"
      style="width: 240px"
      placeholder="请选择付款日期范围"
    />
  </a-form-item>

  <a-form-item label="回购日期" class="smart-query-form-item">
    <a-range-picker
      ref="repurchaseDatePicker"
      v-model:value="repurchaseDateRange"
      @change="onChangeRepurchaseDate"
      :presets="defaultTimeRanges"
      :disabledDate="disabledRepurchaseDate"
      style="width: 240px"
      placeholder="请选择回购日期范围"
    />
  </a-form-item>
</template>
```

#### 2. 数据绑定与状态管理
```javascript
// 查询表单状态
const queryForm = reactive({
  paymentDateBegin: null,
  paymentDateEnd: null,
  repurchaseDateBegin: null,
  repurchaseDateEnd: null,
  // ... 其他字段
});

// 日期范围响应式变量
const paymentDateRange = ref([]);
const repurchaseDateRange = ref([]);
```

#### 3. 事件处理函数
```javascript
// 付款日期变更处理
function onChangePaymentDate(dates) {
  if (dates && dates.length === 2) {
    queryForm.paymentDateBegin = dates[0];
    queryForm.paymentDateEnd = dates[1];

    // 自动同步回购日期
    repurchaseDateRange.value = [dates[0], dates[1]];
    queryForm.repurchaseDateBegin = dates[0];
    queryForm.repurchaseDateEnd = dates[1];
  } else {
    // 清空相关日期
    queryForm.paymentDateBegin = null;
    queryForm.paymentDateEnd = null;
    repurchaseDateRange.value = [];
    queryForm.repurchaseDateBegin = null;
    queryForm.repurchaseDateEnd = null;
  }
}

// 回购日期变更处理
function onChangeRepurchaseDate(dates) {
  if (dates && dates.length === 2) {
    queryForm.repurchaseDateBegin = dates[0];
    queryForm.repurchaseDateEnd = dates[1];
  } else {
    queryForm.repurchaseDateBegin = null;
    queryForm.repurchaseDateEnd = null;
  }
}
```

### 业务价值

#### 1. 精确复购分析
- **时间窗口控制**: 可以分析特定时间段内的回购行为
- **季节性分析**: 支持分析节假日、促销期等特定时期的回购模式
- **周期性研究**: 便于研究不同时间段的客户回购周期

#### 2. 营销策略优化
- **精准营销**: 基于特定时间段的回购数据制定营销策略
- **活动效果评估**: 评估特定营销活动对回购行为的影响
- **客户生命周期管理**: 更好地理解客户在不同时期的购买行为

#### 3. 数据分析增强
- **对比分析**: 可以对比不同时间段的回购表现
- **趋势分析**: 识别回购行为的时间趋势和模式
- **异常检测**: 发现特定时期的异常回购行为

### 用户体验设计

#### 1. 直观的约束提示
- **视觉禁用**: 超出范围的日期显示为灰色不可选状态
- **即时反馈**: 点击禁用日期时立即显示具体的约束说明
- **智能提示**: 根据点击位置显示不同的提示信息

#### 2. 便捷的操作流程
- **一键同步**: 选择付款日期后回购日期自动同步
- **智能调整**: 付款日期变更时回购日期自动适应
- **快速重置**: 支持一键清空所有日期选择

#### 3. 一致的交互体验
- **统一样式**: 与付款日期选择器保持一致的外观和行为
- **预设范围**: 支持相同的快速时间范围选择
- **响应式设计**: 适配不同屏幕尺寸和设备

### 技术特点

#### 1. 高性能实现
- **事件委托**: 使用事件委托减少事件监听器数量
- **防抖处理**: 避免频繁的提示消息显示
- **内存管理**: 正确的组件生命周期管理

#### 2. 兼容性保证
- **渐进增强**: 功能失效时不影响基本的日期选择
- **浏览器兼容**: 支持主流浏览器的日期选择功能
- **降级处理**: 在不支持的环境中优雅降级

#### 3. 可维护性
- **模块化设计**: 功能独立，易于维护和扩展
- **清晰的代码结构**: 逻辑清晰，注释完整
- **测试友好**: 便于编写单元测试和集成测试

## �📞 联系信息

如有问题，请联系开发团队或查看相关代码文件：
- 前端: `smart-admin-web-javascript/src/views/business/customer/customer-link.vue`
- 后端: `smart-admin-api-java17-springboot3/sa-admin/src/main/java/net/lab1024/sa/admin/module/business/customer/`
- SQL: `CustomerLinkMapper.xml`
